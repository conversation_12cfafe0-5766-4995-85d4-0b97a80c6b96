"""
Enhanced Bird Classification CNN Implementation
Incorporating improvements identified from melon classification analysis
"""

import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, Dropout, BatchNormalization
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau, CSVLogger
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix
import time
import os

class EnhancedBirdClassifier:
    """Enhanced Bird Classification with improvements from comparative analysis"""
    
    def __init__(self, input_shape=(224, 224, 3), num_classes=5):
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.model = None
        self.history = None
        
    def create_enhanced_model(self):
        """Create enhanced model with batch normalization and optimized architecture"""
        
        model = Sequential([
            # Block 1 - Paired convolutions for complex feature extraction
            Conv2D(32, (3,3), activation='relu', padding='same', input_shape=self.input_shape),
            BatchNormalization(),
            Conv2D(32, (3,3), activation='relu', padding='same'),
            BatchNormalization(),
            MaxPooling2D(2,2),
            Dropout(0.25),
            
            # Block 2 - Continue paired approach for mid-level features
            Conv2D(64, (3,3), activation='relu', padding='same'),
            BatchNormalization(),
            Conv2D(64, (3,3), activation='relu', padding='same'),
            BatchNormalization(),
            MaxPooling2D(2,2),
            Dropout(0.3),
            
            # Block 3 - Paired convolutions for high-level features
            Conv2D(128, (3,3), activation='relu', padding='same'),
            BatchNormalization(),
            Conv2D(128, (3,3), activation='relu', padding='same'),
            BatchNormalization(),
            MaxPooling2D(2,2),
            Dropout(0.3),
            
            # Block 4 - Single convolution for efficiency (inspired by melon CNN)
            Conv2D(256, (3,3), activation='relu', padding='same'),
            BatchNormalization(),
            MaxPooling2D(2,2),
            Dropout(0.4),
            
            # Classifier - Streamlined (inspired by melon CNN efficiency)
            Flatten(),
            Dense(256, activation='relu'),
            BatchNormalization(),
            Dropout(0.5),
            Dense(self.num_classes, activation='softmax')
        ])
        
        self.model = model
        return model
    
    def create_enhanced_data_generators(self, train_dir, test_dir):
        """Enhanced data augmentation inspired by melon CNN"""
        
        # Enhanced training augmentation (more aggressive like melon CNN)
        train_datagen = ImageDataGenerator(
            rescale=1./255,
            rotation_range=20,          # Increased from 15
            width_shift_range=0.15,     # Increased from 0.1
            height_shift_range=0.15,    # Increased from 0.1
            shear_range=0.15,           # Increased from 0.1
            zoom_range=0.2,             # Increased from 0.15
            horizontal_flip=True,
            vertical_flip=False,        # Keep False for birds
            brightness_range=[0.8, 1.2], # New: brightness variation
            channel_shift_range=0.1,    # New: color variation
            fill_mode='nearest',
            validation_split=0.3
        )
        
        # Test data (no augmentation)
        test_datagen = ImageDataGenerator(rescale=1./255)
        
        # Create generators
        train_generator = train_datagen.flow_from_directory(
            train_dir,
            target_size=self.input_shape[:2],
            batch_size=16,
            class_mode='categorical',
            subset='training'
        )
        
        validation_generator = train_datagen.flow_from_directory(
            train_dir,
            target_size=self.input_shape[:2],
            batch_size=16,
            class_mode='categorical',
            subset='validation'
        )
        
        test_generator = test_datagen.flow_from_directory(
            test_dir,
            target_size=self.input_shape[:2],
            batch_size=16,
            class_mode='categorical',
            shuffle=False
        )
        
        return train_generator, validation_generator, test_generator
    
    def compile_model(self):
        """Compile model with mixed precision for efficiency"""
        
        # Enable mixed precision for faster training
        policy = tf.keras.mixed_precision.Policy('mixed_float16')
        tf.keras.mixed_precision.set_global_policy(policy)
        
        self.model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy', 'top_2_accuracy']
        )
    
    def create_callbacks(self):
        """Create comprehensive callback system"""
        
        callbacks = [
            ModelCheckpoint(
                'best_enhanced_bird_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            ),
            EarlyStopping(
                monitor='val_accuracy',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            ),
            CSVLogger('enhanced_training_history.csv'),
            EfficiencyMonitorCallback()
        ]
        
        return callbacks
    
    def train_model(self, train_generator, validation_generator, epochs=50):
        """Train the enhanced model"""
        
        callbacks = self.create_callbacks()
        
        self.history = self.model.fit(
            train_generator,
            epochs=epochs,
            validation_data=validation_generator,
            callbacks=callbacks,
            verbose=1
        )
        
        return self.history
    
    def evaluate_model(self, test_generator, class_names):
        """Comprehensive model evaluation"""
        
        # Load best model
        self.model = tf.keras.models.load_model('best_enhanced_bird_model.h5')
        
        # Predictions
        predictions = self.model.predict(test_generator)
        predicted_classes = np.argmax(predictions, axis=1)
        true_classes = test_generator.classes
        
        # Classification Report
        print("Enhanced Bird Classification Results:")
        print("="*50)
        print(classification_report(true_classes, predicted_classes, target_names=class_names))
        
        # Confusion Matrix
        plt.figure(figsize=(10, 8))
        cm = confusion_matrix(true_classes, predicted_classes)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                    xticklabels=class_names, yticklabels=class_names)
        plt.title('Enhanced Bird Classification - Confusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.tight_layout()
        plt.savefig('enhanced_bird_confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Model efficiency metrics
        model_size_mb = self.model.count_params() * 4 / (1024 * 1024)
        accuracy = np.mean(predicted_classes == true_classes)
        
        print(f"\nModel Performance Summary:")
        print(f"Overall Accuracy: {accuracy:.4f}")
        print(f"Model Parameters: {self.model.count_params():,}")
        print(f"Model Size: {model_size_mb:.2f} MB")
        
        return {
            'accuracy': accuracy,
            'model_size_mb': model_size_mb,
            'predictions': predictions,
            'confusion_matrix': cm
        }
    
    def plot_training_history(self):
        """Plot comprehensive training history"""
        
        if self.history is None:
            print("No training history available. Train the model first.")
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Accuracy plot
        ax1.plot(self.history.history['accuracy'], label='Training Accuracy', color='blue')
        ax1.plot(self.history.history['val_accuracy'], label='Validation Accuracy', color='red')
        ax1.set_title('Model Accuracy')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        ax1.grid(True)
        
        # Loss plot
        ax2.plot(self.history.history['loss'], label='Training Loss', color='blue')
        ax2.plot(self.history.history['val_loss'], label='Validation Loss', color='red')
        ax2.set_title('Model Loss')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True)
        
        # Top-2 Accuracy plot
        if 'top_2_accuracy' in self.history.history:
            ax3.plot(self.history.history['top_2_accuracy'], label='Training Top-2', color='blue')
            ax3.plot(self.history.history['val_top_2_accuracy'], label='Validation Top-2', color='red')
            ax3.set_title('Top-2 Accuracy')
            ax3.set_xlabel('Epoch')
            ax3.set_ylabel('Top-2 Accuracy')
            ax3.legend()
            ax3.grid(True)
        
        # Learning rate plot (if available)
        if 'lr' in self.history.history:
            ax4.plot(self.history.history['lr'], label='Learning Rate', color='green')
            ax4.set_title('Learning Rate Schedule')
            ax4.set_xlabel('Epoch')
            ax4.set_ylabel('Learning Rate')
            ax4.set_yscale('log')
            ax4.legend()
            ax4.grid(True)
        
        plt.tight_layout()
        plt.savefig('enhanced_bird_training_history.png', dpi=300, bbox_inches='tight')
        plt.show()

class EfficiencyMonitorCallback(tf.keras.callbacks.Callback):
    """Monitor training efficiency metrics inspired by melon CNN's efficiency focus"""
    
    def on_epoch_begin(self, epoch, logs=None):
        self.epoch_start_time = time.time()
    
    def on_epoch_end(self, epoch, logs=None):
        epoch_time = time.time() - self.epoch_start_time
        
        if logs:
            val_acc = logs.get('val_accuracy', 0)
            efficiency_score = val_acc / (epoch_time / 60)  # Accuracy per minute
            
            print(f"Epoch {epoch + 1} Efficiency:")
            print(f"  Time: {epoch_time:.1f}s")
            print(f"  Val Accuracy: {val_acc:.4f}")
            print(f"  Efficiency Score: {efficiency_score:.3f} acc/min")

def create_production_optimized_model(model_path):
    """Create production-optimized versions of the model"""
    
    # Load the trained model
    model = tf.keras.models.load_model(model_path)
    
    # 1. TensorFlow Lite conversion
    converter = tf.lite.TFLiteConverter.from_keras_model(model)
    converter.optimizations = [tf.lite.Optimize.DEFAULT]
    converter.target_spec.supported_types = [tf.float16]
    
    tflite_model = converter.convert()
    
    # Save TFLite model
    with open('enhanced_bird_classifier.tflite', 'wb') as f:
        f.write(tflite_model)
    
    # 2. SavedModel format for serving
    tf.saved_model.save(model, 'enhanced_bird_classifier_serving')
    
    # Size comparison
    original_size = os.path.getsize(model_path) / (1024 * 1024)
    tflite_size = len(tflite_model) / (1024 * 1024)
    
    print(f"Model Optimization Results:")
    print(f"Original Model: {original_size:.2f} MB")
    print(f"TFLite Model: {tflite_size:.2f} MB")
    print(f"Size Reduction: {((original_size - tflite_size) / original_size * 100):.1f}%")
    
    return tflite_model

# Example usage
if __name__ == "__main__":
    # Initialize enhanced classifier
    classifier = EnhancedBirdClassifier()
    
    # Create and compile model
    model = classifier.create_enhanced_model()
    classifier.compile_model()
    
    print("Enhanced Bird Classification Model Created")
    print(f"Total Parameters: {model.count_params():,}")
    print(f"Estimated Model Size: {model.count_params() * 4 / (1024*1024):.2f} MB")
    
    # Model summary
    model.summary()
