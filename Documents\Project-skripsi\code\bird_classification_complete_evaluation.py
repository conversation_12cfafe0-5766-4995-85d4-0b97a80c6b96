"""
Komponen Evaluasi Lengkap untuk Bird Classification CNN
Implementasi komponen yang missing untuk kelengkapan skripsi
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_curve, auc
)
from sklearn.preprocessing import label_binarize
import tensorflow as tf
from tensorflow.keras.preprocessing import image

class BirdClassificationEvaluator:
    """Evaluator lengkap untuk Bird Classification CNN"""
    
    def __init__(self, model_path, class_names):
        self.model = tf.keras.models.load_model(model_path)
        self.class_names = class_names
        self.n_classes = len(class_names)
        
    def analyze_dataset_distribution(self, train_dir, test_dir):
        """1. Exploratory Data Analysis (EDA) - WAJIB untuk skripsi"""
        
        print("=== ANALISIS DISTRIBUSI DATASET ===")
        
        # Hitung distribusi data training
        train_counts = {}
        for class_name in os.listdir(train_dir):
            class_path = os.path.join(train_dir, class_name)
            if os.path.isdir(class_path):
                train_counts[class_name] = len(os.listdir(class_path))
        
        # Hitung distribusi data testing
        test_counts = {}
        for class_name in os.listdir(test_dir):
            class_path = os.path.join(test_dir, class_name)
            if os.path.isdir(class_path):
                test_counts[class_name] = len(os.listdir(class_path))
        
        # Print statistik
        total_train = sum(train_counts.values())
        total_test = sum(test_counts.values())
        
        print(f"Total Training Images: {total_train}")
        print(f"Total Testing Images: {total_test}")
        print(f"Total Dataset: {total_train + total_test}")
        
        print("\nDistribusi per Kelas:")
        for class_name in self.class_names:
            train_count = train_counts.get(class_name, 0)
            test_count = test_counts.get(class_name, 0)
            total_count = train_count + test_count
            percentage = (total_count / (total_train + total_test)) * 100
            print(f"{class_name}: {train_count} train, {test_count} test, {total_count} total ({percentage:.1f}%)")
        
        # Visualisasi distribusi
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # Training distribution
        ax1.bar(range(len(train_counts)), list(train_counts.values()), 
                color='skyblue', alpha=0.8)
        ax1.set_title('Distribusi Data Training per Kelas', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Kelas Burung', fontsize=12)
        ax1.set_ylabel('Jumlah Gambar', fontsize=12)
        ax1.set_xticks(range(len(train_counts)))
        ax1.set_xticklabels(list(train_counts.keys()), rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for i, v in enumerate(train_counts.values()):
            ax1.text(i, v + max(train_counts.values()) * 0.01, str(v), 
                    ha='center', va='bottom', fontweight='bold')
        
        # Testing distribution
        ax2.bar(range(len(test_counts)), list(test_counts.values()), 
                color='lightcoral', alpha=0.8)
        ax2.set_title('Distribusi Data Testing per Kelas', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Kelas Burung', fontsize=12)
        ax2.set_ylabel('Jumlah Gambar', fontsize=12)
        ax2.set_xticks(range(len(test_counts)))
        ax2.set_xticklabels(list(test_counts.keys()), rotation=45, ha='right')
        ax2.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for i, v in enumerate(test_counts.values()):
            ax2.text(i, v + max(test_counts.values()) * 0.01, str(v), 
                    ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('dataset_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Tampilkan sample gambar per kelas
        self._show_sample_images(train_dir, train_counts)
        
        return train_counts, test_counts
    
    def _show_sample_images(self, train_dir, class_counts):
        """Tampilkan sample gambar dari setiap kelas"""
        
        n_classes = len(class_counts)
        cols = 3
        rows = (n_classes + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(15, 5 * rows))
        if rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, (class_name, count) in enumerate(class_counts.items()):
            row = i // cols
            col = i % cols
            
            # Ambil sample gambar pertama dari kelas
            class_path = os.path.join(train_dir, class_name)
            if os.path.exists(class_path):
                sample_files = os.listdir(class_path)
                if sample_files:
                    sample_path = os.path.join(class_path, sample_files[0])
                    
                    try:
                        img = plt.imread(sample_path)
                        axes[row, col].imshow(img)
                        axes[row, col].set_title(f'{class_name}\n({count} gambar)', 
                                               fontsize=12, fontweight='bold')
                        axes[row, col].axis('off')
                    except:
                        axes[row, col].text(0.5, 0.5, f'{class_name}\n(Error loading image)', 
                                          ha='center', va='center', transform=axes[row, col].transAxes)
                        axes[row, col].axis('off')
        
        # Hide unused subplots
        for i in range(n_classes, rows * cols):
            row = i // cols
            col = i % cols
            axes[row, col].axis('off')
        
        plt.suptitle('Sample Gambar per Kelas Burung', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('sample_images_per_class.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_training_history(self, history_csv_path=None, history_dict=None):
        """2. Training History Visualization - WAJIB untuk skripsi"""
        
        print("=== VISUALISASI TRAINING HISTORY ===")
        
        # Load history data
        if history_csv_path and os.path.exists(history_csv_path):
            history_df = pd.read_csv(history_csv_path)
            history = {
                'accuracy': history_df['accuracy'].tolist(),
                'val_accuracy': history_df['val_accuracy'].tolist(),
                'loss': history_df['loss'].tolist(),
                'val_loss': history_df['val_loss'].tolist()
            }
        elif history_dict:
            history = history_dict
        else:
            print("Error: No training history data provided!")
            return
        
        epochs = range(1, len(history['accuracy']) + 1)
        
        # Create comprehensive training plots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # Accuracy plot
        ax1.plot(epochs, history['accuracy'], 'b-', linewidth=2, label='Training Accuracy')
        ax1.plot(epochs, history['val_accuracy'], 'r-', linewidth=2, label='Validation Accuracy')
        ax1.set_title('Model Accuracy', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Epoch', fontsize=12)
        ax1.set_ylabel('Accuracy', fontsize=12)
        ax1.legend(fontsize=11)
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim([0, 1])
        
        # Loss plot
        ax2.plot(epochs, history['loss'], 'b-', linewidth=2, label='Training Loss')
        ax2.plot(epochs, history['val_loss'], 'r-', linewidth=2, label='Validation Loss')
        ax2.set_title('Model Loss', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Epoch', fontsize=12)
        ax2.set_ylabel('Loss', fontsize=12)
        ax2.legend(fontsize=11)
        ax2.grid(True, alpha=0.3)
        
        # Accuracy difference plot
        acc_diff = np.array(history['accuracy']) - np.array(history['val_accuracy'])
        ax3.plot(epochs, acc_diff, 'g-', linewidth=2, label='Training - Validation')
        ax3.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax3.set_title('Accuracy Difference (Overfitting Indicator)', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Epoch', fontsize=12)
        ax3.set_ylabel('Accuracy Difference', fontsize=12)
        ax3.legend(fontsize=11)
        ax3.grid(True, alpha=0.3)
        
        # Learning curve summary
        final_train_acc = history['accuracy'][-1]
        final_val_acc = history['val_accuracy'][-1]
        best_val_acc = max(history['val_accuracy'])
        best_epoch = history['val_accuracy'].index(best_val_acc) + 1
        
        summary_text = f"""Training Summary:
Final Training Accuracy: {final_train_acc:.4f}
Final Validation Accuracy: {final_val_acc:.4f}
Best Validation Accuracy: {best_val_acc:.4f} (Epoch {best_epoch})
Overfitting Gap: {final_train_acc - final_val_acc:.4f}"""
        
        ax4.text(0.1, 0.5, summary_text, transform=ax4.transAxes, fontsize=12,
                verticalalignment='center', bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
        ax4.set_title('Training Summary', fontsize=14, fontweight='bold')
        ax4.axis('off')
        
        plt.tight_layout()
        plt.savefig('training_history_comprehensive.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Print summary statistics
        print(f"Training completed in {len(epochs)} epochs")
        print(f"Best validation accuracy: {best_val_acc:.4f} at epoch {best_epoch}")
        print(f"Final training accuracy: {final_train_acc:.4f}")
        print(f"Final validation accuracy: {final_val_acc:.4f}")
        print(f"Overfitting gap: {final_train_acc - final_val_acc:.4f}")
        
        return history
    
    def comprehensive_evaluation(self, test_generator):
        """3. Comprehensive Model Evaluation - WAJIB untuk skripsi"""
        
        print("=== EVALUASI MODEL KOMPREHENSIF ===")
        
        # Get predictions
        print("Melakukan prediksi pada test set...")
        predictions = self.model.predict(test_generator, verbose=1)
        predicted_classes = np.argmax(predictions, axis=1)
        true_classes = test_generator.classes
        
        # Calculate metrics
        accuracy = accuracy_score(true_classes, predicted_classes)
        precision_macro = precision_score(true_classes, predicted_classes, average='macro')
        recall_macro = recall_score(true_classes, predicted_classes, average='macro')
        f1_macro = f1_score(true_classes, predicted_classes, average='macro')
        
        precision_weighted = precision_score(true_classes, predicted_classes, average='weighted')
        recall_weighted = recall_score(true_classes, predicted_classes, average='weighted')
        f1_weighted = f1_score(true_classes, predicted_classes, average='weighted')
        
        # Print overall metrics
        print("\n=== OVERALL PERFORMANCE METRICS ===")
        print(f"Overall Accuracy: {accuracy:.4f}")
        print(f"Macro-average Precision: {precision_macro:.4f}")
        print(f"Macro-average Recall: {recall_macro:.4f}")
        print(f"Macro-average F1-score: {f1_macro:.4f}")
        print(f"Weighted-average Precision: {precision_weighted:.4f}")
        print(f"Weighted-average Recall: {recall_weighted:.4f}")
        print(f"Weighted-average F1-score: {f1_weighted:.4f}")
        
        # Detailed classification report
        print("\n=== DETAILED CLASSIFICATION REPORT ===")
        cls_report = classification_report(true_classes, predicted_classes, 
                                         target_names=self.class_names, digits=4)
        print(cls_report)
        
        # Per-class accuracy analysis
        print("\n=== PER-CLASS ACCURACY ANALYSIS ===")
        cm = confusion_matrix(true_classes, predicted_classes)
        class_accuracies = cm.diagonal() / cm.sum(axis=1)
        
        for i, (class_name, acc) in enumerate(zip(self.class_names, class_accuracies)):
            total_samples = cm.sum(axis=1)[i]
            correct_predictions = cm.diagonal()[i]
            print(f"{class_name}: {acc:.4f} ({correct_predictions}/{total_samples})")
        
        # Model efficiency metrics
        model_size_mb = self.model.count_params() * 4 / (1024 * 1024)
        print(f"\n=== MODEL EFFICIENCY ===")
        print(f"Total Parameters: {self.model.count_params():,}")
        print(f"Model Size: {model_size_mb:.2f} MB")
        
        return {
            'predictions': predictions,
            'predicted_classes': predicted_classes,
            'true_classes': true_classes,
            'accuracy': accuracy,
            'precision_macro': precision_macro,
            'recall_macro': recall_macro,
            'f1_macro': f1_macro,
            'class_accuracies': class_accuracies,
            'confusion_matrix': cm,
            'classification_report': cls_report
        }

    def plot_confusion_matrix(self, true_classes, predicted_classes, save_path='confusion_matrix.png'):
        """4. Confusion Matrix Visualization - WAJIB untuk skripsi"""

        print("=== CONFUSION MATRIX ANALYSIS ===")

        cm = confusion_matrix(true_classes, predicted_classes)

        # Calculate percentages
        cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100

        # Create figure with two subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))

        # Confusion matrix with counts
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                    xticklabels=self.class_names, yticklabels=self.class_names,
                    cbar_kws={'label': 'Count'}, ax=ax1)
        ax1.set_title('Confusion Matrix (Counts)', fontsize=16, fontweight='bold')
        ax1.set_xlabel('Predicted Label', fontsize=12)
        ax1.set_ylabel('True Label', fontsize=12)

        # Confusion matrix with percentages
        sns.heatmap(cm_percent, annot=True, fmt='.1f', cmap='Reds',
                    xticklabels=self.class_names, yticklabels=self.class_names,
                    cbar_kws={'label': 'Percentage (%)'}, ax=ax2)
        ax2.set_title('Confusion Matrix (Percentages)', fontsize=16, fontweight='bold')
        ax2.set_xlabel('Predicted Label', fontsize=12)
        ax2.set_ylabel('True Label', fontsize=12)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

        # Analysis of confusion matrix
        print("\n=== CONFUSION MATRIX INSIGHTS ===")

        # Find most confused classes
        np.fill_diagonal(cm, 0)  # Remove diagonal for analysis
        max_confusion_idx = np.unravel_index(np.argmax(cm), cm.shape)
        max_confusion_count = cm[max_confusion_idx]

        print(f"Most confused classes: {self.class_names[max_confusion_idx[0]]} → {self.class_names[max_confusion_idx[1]]} ({max_confusion_count} cases)")

        # Find classes with highest misclassification rates
        total_per_class = cm.sum(axis=1) + np.diag(confusion_matrix(true_classes, predicted_classes))
        misclass_rates = cm.sum(axis=1) / total_per_class
        worst_class_idx = np.argmax(misclass_rates)

        print(f"Class with highest misclassification rate: {self.class_names[worst_class_idx]} ({misclass_rates[worst_class_idx]:.2%})")

        return cm, cm_percent

    def plot_roc_curves(self, true_classes, predictions, save_path='roc_curves.png'):
        """5. ROC Curve Analysis - WAJIB untuk skripsi"""

        print("=== ROC CURVE ANALYSIS ===")

        # Binarize labels for multi-class ROC
        y_test_binarized = label_binarize(true_classes, classes=range(self.n_classes))

        # Compute ROC curve and AUC for each class
        fpr = {}
        tpr = {}
        roc_auc = {}

        for i in range(self.n_classes):
            fpr[i], tpr[i], _ = roc_curve(y_test_binarized[:, i], predictions[:, i])
            roc_auc[i] = auc(fpr[i], tpr[i])

        # Compute micro-average ROC curve and AUC
        fpr["micro"], tpr["micro"], _ = roc_curve(y_test_binarized.ravel(), predictions.ravel())
        roc_auc["micro"] = auc(fpr["micro"], tpr["micro"])

        # Compute macro-average ROC curve and AUC
        all_fpr = np.unique(np.concatenate([fpr[i] for i in range(self.n_classes)]))
        mean_tpr = np.zeros_like(all_fpr)
        for i in range(self.n_classes):
            mean_tpr += np.interp(all_fpr, fpr[i], tpr[i])
        mean_tpr /= self.n_classes

        fpr["macro"] = all_fpr
        tpr["macro"] = mean_tpr
        roc_auc["macro"] = auc(fpr["macro"], tpr["macro"])

        # Plot ROC curves
        plt.figure(figsize=(14, 10))

        # Colors for each class
        colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']

        # Plot ROC curve for each class
        for i in range(self.n_classes):
            color = colors[i % len(colors)]
            plt.plot(fpr[i], tpr[i], color=color, linewidth=2,
                    label=f'{self.class_names[i]} (AUC = {roc_auc[i]:.4f})')

        # Plot micro and macro averages
        plt.plot(fpr["micro"], tpr["micro"], color='deeppink', linestyle=':', linewidth=3,
                label=f'Micro-average (AUC = {roc_auc["micro"]:.4f})')
        plt.plot(fpr["macro"], tpr["macro"], color='navy', linestyle=':', linewidth=3,
                label=f'Macro-average (AUC = {roc_auc["macro"]:.4f})')

        # Plot random classifier line
        plt.plot([0, 1], [0, 1], 'k--', linewidth=2, label='Random Classifier (AUC = 0.5000)')

        # Formatting
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate', fontsize=14)
        plt.ylabel('True Positive Rate', fontsize=14)
        plt.title('Multi-class ROC Curves - Bird Classification', fontsize=16, fontweight='bold')
        plt.legend(loc="lower right", fontsize=10)
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

        # Print AUC summary
        print("\n=== AUC SCORES SUMMARY ===")
        for i, class_name in enumerate(self.class_names):
            print(f"{class_name}: {roc_auc[i]:.4f}")

        print(f"\nMicro-average AUC: {roc_auc['micro']:.4f}")
        print(f"Macro-average AUC: {roc_auc['macro']:.4f}")

        # Performance interpretation
        macro_auc = roc_auc['macro']
        if macro_auc >= 0.9:
            performance = "Excellent"
        elif macro_auc >= 0.8:
            performance = "Good"
        elif macro_auc >= 0.7:
            performance = "Fair"
        else:
            performance = "Poor"

        print(f"\nOverall Model Performance: {performance} (Macro-AUC: {macro_auc:.4f})")

        return roc_auc, fpr, tpr

    def create_performance_summary(self, evaluation_results, roc_auc_scores):
        """6. Performance Summary Report - PENTING untuk skripsi"""

        print("=== PERFORMANCE SUMMARY REPORT ===")

        # Extract metrics
        accuracy = evaluation_results['accuracy']
        precision = evaluation_results['precision_macro']
        recall = evaluation_results['recall_macro']
        f1 = evaluation_results['f1_macro']
        class_accuracies = evaluation_results['class_accuracies']

        # Model info
        model_size_mb = self.model.count_params() * 4 / (1024 * 1024)

        # Create summary table
        summary_data = {
            'Metric': [
                'Overall Accuracy',
                'Macro-avg Precision',
                'Macro-avg Recall',
                'Macro-avg F1-Score',
                'Macro-avg AUC',
                'Best Class Accuracy',
                'Worst Class Accuracy',
                'Model Size (MB)',
                'Total Parameters'
            ],
            'Value': [
                f'{accuracy:.4f}',
                f'{precision:.4f}',
                f'{recall:.4f}',
                f'{f1:.4f}',
                f'{roc_auc_scores["macro"]:.4f}',
                f'{max(class_accuracies):.4f}',
                f'{min(class_accuracies):.4f}',
                f'{model_size_mb:.2f}',
                f'{self.model.count_params():,}'
            ]
        }

        df_summary = pd.DataFrame(summary_data)
        print("\nModel Performance Summary:")
        print("=" * 50)
        print(df_summary.to_string(index=False))

        # Visualize per-class performance
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # Per-class accuracy
        bars1 = ax1.bar(range(len(self.class_names)), class_accuracies,
                       color='skyblue', alpha=0.8, edgecolor='navy')
        ax1.set_title('Per-Class Accuracy', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Bird Classes', fontsize=12)
        ax1.set_ylabel('Accuracy', fontsize=12)
        ax1.set_xticks(range(len(self.class_names)))
        ax1.set_xticklabels(self.class_names, rotation=45, ha='right')
        ax1.set_ylim([0, 1])
        ax1.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar, acc in zip(bars1, class_accuracies):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')

        # Per-class AUC scores
        class_auc_scores = [roc_auc_scores[i] for i in range(self.n_classes)]
        bars2 = ax2.bar(range(len(self.class_names)), class_auc_scores,
                       color='lightcoral', alpha=0.8, edgecolor='darkred')
        ax2.set_title('Per-Class AUC Scores', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Bird Classes', fontsize=12)
        ax2.set_ylabel('AUC Score', fontsize=12)
        ax2.set_xticks(range(len(self.class_names)))
        ax2.set_xticklabels(self.class_names, rotation=45, ha='right')
        ax2.set_ylim([0, 1])
        ax2.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar, auc_score in zip(bars2, class_auc_scores):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{auc_score:.3f}', ha='center', va='bottom', fontweight='bold')

        # Overall metrics comparison
        metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'AUC']
        values = [accuracy, precision, recall, f1, roc_auc_scores['macro']]

        bars3 = ax3.bar(metrics, values, color=['gold', 'lightgreen', 'lightblue', 'plum', 'orange'],
                       alpha=0.8, edgecolor='black')
        ax3.set_title('Overall Performance Metrics', fontsize=14, fontweight='bold')
        ax3.set_ylabel('Score', fontsize=12)
        ax3.set_ylim([0, 1])
        ax3.grid(True, alpha=0.3)

        # Add value labels
        for bar, value in zip(bars3, values):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

        # Model complexity visualization
        complexity_metrics = ['Parameters (M)', 'Size (MB)']
        complexity_values = [self.model.count_params() / 1e6, model_size_mb]

        bars4 = ax4.bar(complexity_metrics, complexity_values,
                       color=['mediumpurple', 'mediumseagreen'], alpha=0.8, edgecolor='black')
        ax4.set_title('Model Complexity', fontsize=14, fontweight='bold')
        ax4.set_ylabel('Value', fontsize=12)
        ax4.grid(True, alpha=0.3)

        # Add value labels
        for bar, value in zip(bars4, complexity_values):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.05,
                    f'{value:.2f}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig('performance_summary_comprehensive.png', dpi=300, bbox_inches='tight')
        plt.show()

        return df_summary

# Example usage function
def run_complete_evaluation(model_path, train_dir, test_dir, class_names,
                          test_generator, history_csv_path=None):
    """
    Fungsi utama untuk menjalankan evaluasi lengkap

    Parameters:
    - model_path: path ke model .h5
    - train_dir: direktori data training
    - test_dir: direktori data testing
    - class_names: list nama kelas
    - test_generator: generator untuk data testing
    - history_csv_path: path ke file CSV training history (opsional)
    """

    print("🔥 MEMULAI EVALUASI LENGKAP BIRD CLASSIFICATION CNN 🔥")
    print("=" * 60)

    # Initialize evaluator
    evaluator = BirdClassificationEvaluator(model_path, class_names)

    # 1. Dataset Analysis
    train_counts, test_counts = evaluator.analyze_dataset_distribution(train_dir, test_dir)

    # 2. Training History (jika tersedia)
    if history_csv_path:
        evaluator.plot_training_history(history_csv_path=history_csv_path)

    # 3. Comprehensive Evaluation
    eval_results = evaluator.comprehensive_evaluation(test_generator)

    # 4. Confusion Matrix
    cm, cm_percent = evaluator.plot_confusion_matrix(
        eval_results['true_classes'],
        eval_results['predicted_classes']
    )

    # 5. ROC Curves
    roc_auc, fpr, tpr = evaluator.plot_roc_curves(
        eval_results['true_classes'],
        eval_results['predictions']
    )

    # 6. Performance Summary
    summary_df = evaluator.create_performance_summary(eval_results, roc_auc)

    print("\n🎉 EVALUASI LENGKAP SELESAI! 🎉")
    print("Semua visualisasi dan metrik telah disimpan.")
    print("File yang dihasilkan:")
    print("- dataset_distribution.png")
    print("- sample_images_per_class.png")
    print("- training_history_comprehensive.png")
    print("- confusion_matrix.png")
    print("- roc_curves.png")
    print("- performance_summary_comprehensive.png")

    return {
        'evaluator': evaluator,
        'evaluation_results': eval_results,
        'roc_results': roc_auc,
        'summary': summary_df
    }
