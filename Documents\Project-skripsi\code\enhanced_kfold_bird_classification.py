"""
Enhanced K-Fold Cross-Validation for Bird Classification CNN
Comprehensive implementation for thesis requirements
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import KFold, StratifiedKFold
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_curve, auc
)
from sklearn.preprocessing import label_binarize
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Dropout, Flatten, Dense, BatchNormalization
from tensorflow.keras.preprocessing.image import ImageDataGenerator, load_img, img_to_array
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.utils import to_categorical
import warnings
warnings.filterwarnings('ignore')

class EnhancedKFoldBirdClassifier:
    """Enhanced K-Fold Cross-Validation for Bird Classification with comprehensive evaluation"""
    
    def __init__(self, train_dir, class_names, target_size=(224, 224), n_splits=5, random_state=42):
        self.train_dir = train_dir
        self.class_names = class_names
        self.target_size = target_size
        self.n_splits = n_splits
        self.random_state = random_state
        self.num_classes = len(class_names)
        
        # Results storage
        self.fold_results = []
        self.fold_histories = []
        self.fold_predictions = []
        self.fold_true_labels = []
        
    def create_model(self):
        """Create the bird classification CNN model architecture"""
        
        model = Sequential([
            # Block 1 - Paired convolutions for complex feature extraction
            Conv2D(32, (3, 3), activation='relu', padding='same', input_shape=(*self.target_size, 3)),
            BatchNormalization(),
            Conv2D(32, (3, 3), activation='relu', padding='same'),
            BatchNormalization(),
            MaxPooling2D((2, 2)),
            Dropout(0.25),
            
            # Block 2
            Conv2D(64, (3, 3), activation='relu', padding='same'),
            BatchNormalization(),
            Conv2D(64, (3, 3), activation='relu', padding='same'),
            BatchNormalization(),
            MaxPooling2D((2, 2)),
            Dropout(0.25),
            
            # Block 3
            Conv2D(128, (3, 3), activation='relu', padding='same'),
            BatchNormalization(),
            Conv2D(128, (3, 3), activation='relu', padding='same'),
            BatchNormalization(),
            MaxPooling2D((2, 2)),
            Dropout(0.3),
            
            # Block 4
            Conv2D(256, (3, 3), activation='relu', padding='same'),
            BatchNormalization(),
            Conv2D(256, (3, 3), activation='relu', padding='same'),
            BatchNormalization(),
            MaxPooling2D((2, 2)),
            Dropout(0.3),
            
            # Classifier
            Flatten(),
            Dense(512, activation='relu'),
            BatchNormalization(),
            Dropout(0.5),
            Dense(256, activation='relu'),
            BatchNormalization(),
            Dropout(0.5),
            Dense(self.num_classes, activation='softmax')
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy', 'precision', 'recall']
        )
        
        return model
    
    def prepare_data(self):
        """Prepare data for k-fold cross-validation"""
        
        print(f"🔍 Collecting image paths and labels from: {self.train_dir}")
        
        all_image_paths = []
        all_image_labels = []
        
        # Collect all image paths and labels
        for class_idx, class_name in enumerate(self.class_names):
            class_path = os.path.join(self.train_dir, class_name)
            if os.path.exists(class_path):
                image_files = [
                    os.path.join(class_path, f) for f in os.listdir(class_path)
                    if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))
                ]
                all_image_paths.extend(image_files)
                all_image_labels.extend([class_idx] * len(image_files))
        
        if not all_image_paths:
            raise ValueError("No images found in the training directory!")
        
        # Convert to numpy arrays
        self.all_image_paths = np.array(all_image_paths)
        self.all_image_labels = np.array(all_image_labels)
        
        print(f"✅ Found {len(self.all_image_paths)} total training images")
        
        # Print class distribution
        print("\n📊 Class Distribution:")
        for class_idx, class_name in enumerate(self.class_names):
            count = np.sum(self.all_image_labels == class_idx)
            percentage = (count / len(self.all_image_labels)) * 100
            print(f"  {class_name}: {count} images ({percentage:.1f}%)")
        
        return self.all_image_paths, self.all_image_labels
    
    def load_images_batch(self, image_paths, labels):
        """Load and preprocess images for a batch"""
        
        images = []
        for path in image_paths:
            try:
                img = load_img(path, target_size=self.target_size)
                img_array = img_to_array(img) / 255.0
                images.append(img_array)
            except Exception as e:
                print(f"Warning: Could not load image {path}: {e}")
                # Create a black image as placeholder
                images.append(np.zeros((*self.target_size, 3)))
        
        return np.array(images), to_categorical(labels, num_classes=self.num_classes)
    
    def run_kfold_cross_validation(self, epochs=20, batch_size=16, use_stratified=True):
        """Run enhanced k-fold cross-validation with comprehensive evaluation"""
        
        print(f"🚀 Starting Enhanced {self.n_splits}-Fold Cross-Validation for Bird Classification")
        print("=" * 80)
        
        # Prepare data
        self.prepare_data()
        
        # Initialize cross-validation
        if use_stratified:
            kf = StratifiedKFold(n_splits=self.n_splits, shuffle=True, random_state=self.random_state)
            cv_splits = kf.split(self.all_image_paths, self.all_image_labels)
        else:
            kf = KFold(n_splits=self.n_splits, shuffle=True, random_state=self.random_state)
            cv_splits = kf.split(self.all_image_paths)
        
        # Storage for results
        fold_metrics = {
            'accuracy': [], 'precision': [], 'recall': [], 'f1_score': [],
            'loss': [], 'auc_macro': [], 'auc_weighted': []
        }
        
        fold_confusion_matrices = []
        fold_classification_reports = []
        
        # Data augmentation for training
        train_datagen = ImageDataGenerator(
            rotation_range=20,
            width_shift_range=0.15,
            height_shift_range=0.15,
            shear_range=0.15,
            zoom_range=0.2,
            horizontal_flip=True,
            vertical_flip=False,
            brightness_range=[0.8, 1.2],
            channel_shift_range=0.1,
            fill_mode='nearest'
        )
        
        # No augmentation for validation
        val_datagen = ImageDataGenerator()
        
        # Run k-fold cross-validation
        for fold, (train_idx, val_idx) in enumerate(cv_splits):
            print(f"\n🔄 FOLD {fold + 1}/{self.n_splits}")
            print("-" * 50)
            
            # Split data for current fold
            X_train_paths = self.all_image_paths[train_idx]
            X_val_paths = self.all_image_paths[val_idx]
            y_train = self.all_image_labels[train_idx]
            y_val = self.all_image_labels[val_idx]
            
            print(f"Training samples: {len(X_train_paths)}")
            print(f"Validation samples: {len(X_val_paths)}")
            
            # Load images for current fold
            print("📥 Loading images for current fold...")
            X_train_images, y_train_categorical = self.load_images_batch(X_train_paths, y_train)
            X_val_images, y_val_categorical = self.load_images_batch(X_val_paths, y_val)
            
            # Create fresh model for this fold
            print("🏗️ Creating fresh model for current fold...")
            fold_model = self.create_model()
            
            # Callbacks for training
            callbacks = [
                EarlyStopping(
                    monitor='val_accuracy',
                    patience=10,
                    restore_best_weights=True,
                    verbose=0
                ),
                ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=5,
                    min_lr=1e-7,
                    verbose=0
                )
            ]
            
            # Train model
            print(f"🏋️ Training model for fold {fold + 1}...")
            history = fold_model.fit(
                train_datagen.flow(X_train_images, y_train_categorical, batch_size=batch_size),
                epochs=epochs,
                validation_data=val_datagen.flow(X_val_images, y_val_categorical, batch_size=batch_size),
                callbacks=callbacks,
                verbose=0
            )
            
            # Evaluate model
            print(f"📊 Evaluating model for fold {fold + 1}...")
            
            # Get predictions
            val_predictions = fold_model.predict(X_val_images, verbose=0)
            val_pred_classes = np.argmax(val_predictions, axis=1)
            val_true_classes = np.argmax(y_val_categorical, axis=1)
            
            # Calculate metrics
            fold_accuracy = accuracy_score(val_true_classes, val_pred_classes)
            fold_precision = precision_score(val_true_classes, val_pred_classes, average='macro', zero_division=0)
            fold_recall = recall_score(val_true_classes, val_pred_classes, average='macro', zero_division=0)
            fold_f1 = f1_score(val_true_classes, val_pred_classes, average='macro', zero_division=0)
            
            # Calculate AUC scores
            try:
                # Binarize labels for multi-class AUC
                y_val_binarized = label_binarize(val_true_classes, classes=range(self.num_classes))
                
                # Calculate AUC for each class
                auc_scores = []
                for i in range(self.num_classes):
                    if len(np.unique(y_val_binarized[:, i])) > 1:  # Check if both classes are present
                        fpr, tpr, _ = roc_curve(y_val_binarized[:, i], val_predictions[:, i])
                        auc_score = auc(fpr, tpr)
                        auc_scores.append(auc_score)
                    else:
                        auc_scores.append(0.5)  # Default AUC for missing class
                
                fold_auc_macro = np.mean(auc_scores)
                fold_auc_weighted = np.average(auc_scores, weights=[np.sum(val_true_classes == i) for i in range(self.num_classes)])
                
            except Exception as e:
                print(f"Warning: Could not calculate AUC for fold {fold + 1}: {e}")
                fold_auc_macro = 0.0
                fold_auc_weighted = 0.0
            
            # Store results
            fold_metrics['accuracy'].append(fold_accuracy)
            fold_metrics['precision'].append(fold_precision)
            fold_metrics['recall'].append(fold_recall)
            fold_metrics['f1_score'].append(fold_f1)
            fold_metrics['loss'].append(history.history['val_loss'][-1])
            fold_metrics['auc_macro'].append(fold_auc_macro)
            fold_metrics['auc_weighted'].append(fold_auc_weighted)
            
            # Store confusion matrix and classification report
            fold_cm = confusion_matrix(val_true_classes, val_pred_classes)
            fold_confusion_matrices.append(fold_cm)
            
            fold_report = classification_report(
                val_true_classes, val_pred_classes, 
                target_names=self.class_names, 
                output_dict=True, 
                zero_division=0
            )
            fold_classification_reports.append(fold_report)
            
            # Store predictions for later analysis
            self.fold_predictions.append(val_predictions)
            self.fold_true_labels.append(val_true_classes)
            
            # Store training history
            self.fold_histories.append(history.history)
            
            # Print fold results
            print(f"✅ Fold {fold + 1} Results:")
            print(f"   Accuracy: {fold_accuracy:.4f}")
            print(f"   Precision: {fold_precision:.4f}")
            print(f"   Recall: {fold_recall:.4f}")
            print(f"   F1-Score: {fold_f1:.4f}")
            print(f"   AUC (Macro): {fold_auc_macro:.4f}")
            
            # Clear memory
            tf.keras.backend.clear_session()
            del fold_model, X_train_images, X_val_images
        
        # Store results
        self.fold_metrics = fold_metrics
        self.fold_confusion_matrices = fold_confusion_matrices
        self.fold_classification_reports = fold_classification_reports
        
        return self.analyze_kfold_results()
    
    def analyze_kfold_results(self):
        """Analyze and summarize k-fold cross-validation results"""
        
        print(f"\n🎯 K-FOLD CROSS-VALIDATION RESULTS SUMMARY")
        print("=" * 60)
        
        # Calculate statistics for each metric
        results_summary = {}
        
        for metric_name, values in self.fold_metrics.items():
            mean_val = np.mean(values)
            std_val = np.std(values)
            min_val = np.min(values)
            max_val = np.max(values)
            
            results_summary[metric_name] = {
                'mean': mean_val,
                'std': std_val,
                'min': min_val,
                'max': max_val,
                'values': values
            }
            
            print(f"{metric_name.upper()}:")
            print(f"  Mean: {mean_val:.4f} ± {std_val:.4f}")
            print(f"  Range: [{min_val:.4f}, {max_val:.4f}]")
            print(f"  Individual folds: {[f'{v:.4f}' for v in values]}")
            print()
        
        # Statistical significance analysis
        self.perform_statistical_analysis(results_summary)
        
        # Create comprehensive visualizations
        self.plot_kfold_results(results_summary)
        self.plot_fold_confusion_matrices()
        self.plot_kfold_roc_curves()
        self.create_performance_comparison_table()
        
        return results_summary
    
    def perform_statistical_analysis(self, results_summary):
        """Perform statistical significance analysis"""
        
        print("📈 STATISTICAL SIGNIFICANCE ANALYSIS")
        print("-" * 40)
        
        # Confidence intervals (95%)
        from scipy import stats
        
        for metric_name, stats_dict in results_summary.items():
            values = stats_dict['values']
            mean_val = stats_dict['mean']
            std_val = stats_dict['std']
            
            # Calculate 95% confidence interval
            confidence_level = 0.95
            degrees_freedom = len(values) - 1
            confidence_interval = stats.t.interval(
                confidence_level, degrees_freedom, 
                loc=mean_val, scale=stats.sem(values)
            )
            
            print(f"{metric_name.upper()}:")
            print(f"  95% Confidence Interval: [{confidence_interval[0]:.4f}, {confidence_interval[1]:.4f}]")
            print(f"  Standard Error: {stats.sem(values):.4f}")
            
            # Coefficient of variation (relative variability)
            cv = (std_val / mean_val) * 100 if mean_val != 0 else 0
            print(f"  Coefficient of Variation: {cv:.2f}%")
            
            # Interpretation
            if cv < 5:
                stability = "Very Stable"
            elif cv < 10:
                stability = "Stable"
            elif cv < 20:
                stability = "Moderately Stable"
            else:
                stability = "Unstable"
            
            print(f"  Model Stability: {stability}")
            print()
    
    def plot_kfold_results(self, results_summary):
        """Plot comprehensive k-fold results visualization"""
        
        print("📊 Creating K-Fold results visualization...")
        
        # Prepare data for plotting
        metrics_to_plot = ['accuracy', 'precision', 'recall', 'f1_score', 'auc_macro']
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, metric in enumerate(metrics_to_plot):
            if i < len(axes):
                values = results_summary[metric]['values']
                mean_val = results_summary[metric]['mean']
                std_val = results_summary[metric]['std']
                
                # Box plot
                axes[i].boxplot(values, patch_artist=True, 
                               boxprops=dict(facecolor='lightblue', alpha=0.7))
                
                # Add individual points
                x_pos = [1] * len(values)
                axes[i].scatter(x_pos, values, color='red', alpha=0.7, s=50, zorder=3)
                
                # Add mean line
                axes[i].axhline(y=mean_val, color='green', linestyle='--', linewidth=2, 
                               label=f'Mean: {mean_val:.4f}')
                
                # Formatting
                axes[i].set_title(f'{metric.replace("_", " ").title()}\n(Mean ± Std: {mean_val:.4f} ± {std_val:.4f})', 
                                 fontsize=12, fontweight='bold')
                axes[i].set_ylabel('Score')
                axes[i].grid(True, alpha=0.3)
                axes[i].legend()
                
                # Add fold numbers as x-axis labels
                fold_labels = [f'Fold {j+1}\n{values[j]:.3f}' for j in range(len(values))]
                axes[i].set_xticks(range(1, len(values) + 1))
                axes[i].set_xticklabels([f'F{j+1}' for j in range(len(values))])
        
        # Hide unused subplot
        if len(metrics_to_plot) < len(axes):
            axes[-1].axis('off')
        
        plt.suptitle('K-Fold Cross-Validation Results - Bird Classification CNN', 
                     fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('kfold_results_comprehensive.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_fold_confusion_matrices(self):
        """Plot confusion matrices for all folds"""
        
        print("📊 Creating fold-wise confusion matrices...")
        
        n_folds = len(self.fold_confusion_matrices)
        cols = 3
        rows = (n_folds + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(15, 5 * rows))
        if rows == 1:
            axes = axes.reshape(1, -1)
        
        for fold, cm in enumerate(self.fold_confusion_matrices):
            row = fold // cols
            col = fold % cols
            
            # Plot confusion matrix
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=self.class_names, yticklabels=self.class_names,
                       ax=axes[row, col], cbar_kws={'label': 'Count'})
            
            # Calculate fold accuracy
            fold_acc = np.trace(cm) / np.sum(cm)
            axes[row, col].set_title(f'Fold {fold + 1}\nAccuracy: {fold_acc:.4f}', 
                                   fontsize=12, fontweight='bold')
            axes[row, col].set_xlabel('Predicted')
            axes[row, col].set_ylabel('True')
        
        # Hide unused subplots
        for i in range(n_folds, rows * cols):
            row = i // cols
            col = i % cols
            axes[row, col].axis('off')
        
        plt.suptitle('Confusion Matrices for All Folds - Bird Classification', 
                     fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('kfold_confusion_matrices.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Calculate and plot average confusion matrix
        self.plot_average_confusion_matrix()
    
    def plot_average_confusion_matrix(self):
        """Plot average confusion matrix across all folds"""
        
        print("📊 Creating average confusion matrix...")
        
        # Calculate average confusion matrix
        avg_cm = np.mean(self.fold_confusion_matrices, axis=0)
        avg_cm_percent = avg_cm / avg_cm.sum(axis=1)[:, np.newaxis] * 100
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # Average confusion matrix (counts)
        sns.heatmap(avg_cm, annot=True, fmt='.1f', cmap='Blues',
                   xticklabels=self.class_names, yticklabels=self.class_names,
                   ax=ax1, cbar_kws={'label': 'Average Count'})
        ax1.set_title('Average Confusion Matrix (Counts)', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Predicted Label')
        ax1.set_ylabel('True Label')
        
        # Average confusion matrix (percentages)
        sns.heatmap(avg_cm_percent, annot=True, fmt='.1f', cmap='Reds',
                   xticklabels=self.class_names, yticklabels=self.class_names,
                   ax=ax2, cbar_kws={'label': 'Percentage (%)'})
        ax2.set_title('Average Confusion Matrix (Percentages)', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Predicted Label')
        ax2.set_ylabel('True Label')
        
        plt.tight_layout()
        plt.savefig('kfold_average_confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return avg_cm, avg_cm_percent

    def plot_kfold_roc_curves(self):
        """Plot ROC curves analysis for k-fold cross-validation"""

        print("📊 Creating K-Fold ROC curves analysis...")

        # Combine all fold predictions and true labels
        all_predictions = np.vstack(self.fold_predictions)
        all_true_labels = np.concatenate(self.fold_true_labels)

        # Binarize labels
        y_test_binarized = label_binarize(all_true_labels, classes=range(self.num_classes))

        # Calculate ROC curves for each class
        fpr = {}
        tpr = {}
        roc_auc = {}

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))

        # Colors for each class
        colors = ['blue', 'red', 'green', 'orange', 'purple']

        # Plot ROC curve for each class
        for i in range(self.num_classes):
            fpr[i], tpr[i], _ = roc_curve(y_test_binarized[:, i], all_predictions[:, i])
            roc_auc[i] = auc(fpr[i], tpr[i])

            color = colors[i % len(colors)]
            ax1.plot(fpr[i], tpr[i], color=color, linewidth=2,
                    label=f'{self.class_names[i]} (AUC = {roc_auc[i]:.4f})')

        # Compute and plot macro-average ROC curve
        all_fpr = np.unique(np.concatenate([fpr[i] for i in range(self.num_classes)]))
        mean_tpr = np.zeros_like(all_fpr)
        for i in range(self.num_classes):
            mean_tpr += np.interp(all_fpr, fpr[i], tpr[i])
        mean_tpr /= self.num_classes

        fpr["macro"] = all_fpr
        tpr["macro"] = mean_tpr
        roc_auc["macro"] = auc(fpr["macro"], tpr["macro"])

        # Plot macro-average
        ax1.plot(fpr["macro"], tpr["macro"], color='navy', linestyle=':', linewidth=3,
                label=f'Macro-average (AUC = {roc_auc["macro"]:.4f})')

        # Plot random classifier
        ax1.plot([0, 1], [0, 1], 'k--', linewidth=2, label='Random Classifier')

        ax1.set_xlim([0.0, 1.0])
        ax1.set_ylim([0.0, 1.05])
        ax1.set_xlabel('False Positive Rate', fontsize=12)
        ax1.set_ylabel('True Positive Rate', fontsize=12)
        ax1.set_title('K-Fold ROC Curves - All Classes', fontsize=14, fontweight='bold')
        ax1.legend(loc="lower right", fontsize=10)
        ax1.grid(True, alpha=0.3)

        # Plot AUC scores comparison
        class_auc_scores = [roc_auc[i] for i in range(self.num_classes)]
        bars = ax2.bar(range(len(self.class_names)), class_auc_scores,
                      color=colors[:len(self.class_names)], alpha=0.7, edgecolor='black')

        ax2.set_title('AUC Scores per Class (K-Fold Average)', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Bird Classes', fontsize=12)
        ax2.set_ylabel('AUC Score', fontsize=12)
        ax2.set_xticks(range(len(self.class_names)))
        ax2.set_xticklabels(self.class_names, rotation=45, ha='right')
        ax2.set_ylim([0, 1])
        ax2.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar, auc_score in zip(bars, class_auc_scores):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{auc_score:.3f}', ha='center', va='bottom', fontweight='bold')

        # Add macro-average line
        ax2.axhline(y=roc_auc["macro"], color='navy', linestyle='--', linewidth=2,
                   label=f'Macro-average: {roc_auc["macro"]:.4f}')
        ax2.legend()

        plt.tight_layout()
        plt.savefig('kfold_roc_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        return roc_auc

    def create_performance_comparison_table(self):
        """Create detailed performance comparison table"""

        print("📋 Creating performance comparison table...")

        # Create comprehensive results table
        results_data = []

        for fold in range(self.n_splits):
            fold_data = {
                'Fold': f'Fold {fold + 1}',
                'Accuracy': f"{self.fold_metrics['accuracy'][fold]:.4f}",
                'Precision': f"{self.fold_metrics['precision'][fold]:.4f}",
                'Recall': f"{self.fold_metrics['recall'][fold]:.4f}",
                'F1-Score': f"{self.fold_metrics['f1_score'][fold]:.4f}",
                'AUC (Macro)': f"{self.fold_metrics['auc_macro'][fold]:.4f}",
                'Loss': f"{self.fold_metrics['loss'][fold]:.4f}"
            }
            results_data.append(fold_data)

        # Add summary statistics
        summary_data = {
            'Fold': 'MEAN ± STD',
            'Accuracy': f"{np.mean(self.fold_metrics['accuracy']):.4f} ± {np.std(self.fold_metrics['accuracy']):.4f}",
            'Precision': f"{np.mean(self.fold_metrics['precision']):.4f} ± {np.std(self.fold_metrics['precision']):.4f}",
            'Recall': f"{np.mean(self.fold_metrics['recall']):.4f} ± {np.std(self.fold_metrics['recall']):.4f}",
            'F1-Score': f"{np.mean(self.fold_metrics['f1_score']):.4f} ± {np.std(self.fold_metrics['f1_score']):.4f}",
            'AUC (Macro)': f"{np.mean(self.fold_metrics['auc_macro']):.4f} ± {np.std(self.fold_metrics['auc_macro']):.4f}",
            'Loss': f"{np.mean(self.fold_metrics['loss']):.4f} ± {np.std(self.fold_metrics['loss']):.4f}"
        }
        results_data.append(summary_data)

        # Create DataFrame
        df_results = pd.DataFrame(results_data)

        # Display table
        print("\n📊 K-FOLD CROSS-VALIDATION PERFORMANCE TABLE")
        print("=" * 80)
        print(df_results.to_string(index=False))

        # Save to CSV
        df_results.to_csv('kfold_results_summary.csv', index=False)
        print(f"\n💾 Results saved to: kfold_results_summary.csv")

        return df_results

    def generate_thesis_report(self, output_file='kfold_thesis_report.txt'):
        """Generate comprehensive report for thesis documentation"""

        print(f"📝 Generating thesis report...")

        report_content = []
        report_content.append("=" * 80)
        report_content.append("K-FOLD CROSS-VALIDATION REPORT - BIRD CLASSIFICATION CNN")
        report_content.append("=" * 80)
        report_content.append("")

        # Dataset information
        report_content.append("DATASET INFORMATION:")
        report_content.append(f"- Total training images: {len(self.all_image_paths)}")
        report_content.append(f"- Number of classes: {self.num_classes}")
        report_content.append(f"- Image size: {self.target_size}")
        report_content.append(f"- Cross-validation folds: {self.n_splits}")
        report_content.append("")

        # Class distribution
        report_content.append("CLASS DISTRIBUTION:")
        for class_idx, class_name in enumerate(self.class_names):
            count = np.sum(self.all_image_labels == class_idx)
            percentage = (count / len(self.all_image_labels)) * 100
            report_content.append(f"- {class_name}: {count} images ({percentage:.1f}%)")
        report_content.append("")

        # Performance summary
        report_content.append("PERFORMANCE SUMMARY:")
        for metric_name, values in self.fold_metrics.items():
            mean_val = np.mean(values)
            std_val = np.std(values)
            report_content.append(f"- {metric_name.upper()}: {mean_val:.4f} ± {std_val:.4f}")
        report_content.append("")

        # Statistical analysis
        report_content.append("STATISTICAL ANALYSIS:")
        accuracy_values = self.fold_metrics['accuracy']
        from scipy import stats
        confidence_interval = stats.t.interval(
            0.95, len(accuracy_values) - 1,
            loc=np.mean(accuracy_values), scale=stats.sem(accuracy_values)
        )
        report_content.append(f"- 95% Confidence Interval for Accuracy: [{confidence_interval[0]:.4f}, {confidence_interval[1]:.4f}]")
        report_content.append(f"- Standard Error: {stats.sem(accuracy_values):.4f}")
        report_content.append(f"- Coefficient of Variation: {(np.std(accuracy_values)/np.mean(accuracy_values)*100):.2f}%")
        report_content.append("")

        # Model architecture summary
        report_content.append("MODEL ARCHITECTURE:")
        temp_model = self.create_model()
        report_content.append(f"- Total parameters: {temp_model.count_params():,}")
        report_content.append(f"- Model size: {temp_model.count_params() * 4 / (1024*1024):.2f} MB")
        report_content.append(f"- Architecture: Custom CNN with paired convolutions")
        report_content.append("")

        # Recommendations
        report_content.append("RECOMMENDATIONS FOR THESIS:")
        report_content.append("1. Include all generated visualizations in Results section")
        report_content.append("2. Discuss statistical significance of results")
        report_content.append("3. Compare with baseline models or literature")
        report_content.append("4. Address class imbalance if present")
        report_content.append("5. Discuss model complexity vs performance trade-offs")
        report_content.append("")

        # Write report to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_content))

        print(f"✅ Thesis report generated: {output_file}")

        # Also print to console
        print("\n" + '\n'.join(report_content))

        return report_content

# Example usage function for integration into notebook
def run_enhanced_kfold_evaluation(train_dir, class_names, epochs=20, n_splits=5):
    """
    Main function to run enhanced k-fold cross-validation

    Parameters:
    - train_dir: Path to training data directory
    - class_names: List of class names
    - epochs: Number of training epochs per fold
    - n_splits: Number of folds for cross-validation

    Returns:
    - Complete evaluation results and visualizations
    """

    print("🚀 ENHANCED K-FOLD CROSS-VALIDATION FOR BIRD CLASSIFICATION")
    print("=" * 70)

    # Initialize evaluator
    evaluator = EnhancedKFoldBirdClassifier(
        train_dir=train_dir,
        class_names=class_names,
        n_splits=n_splits
    )

    # Run k-fold cross-validation
    results_summary = evaluator.run_kfold_cross_validation(epochs=epochs)

    # Generate thesis report
    evaluator.generate_thesis_report()

    print("\n🎉 ENHANCED K-FOLD EVALUATION COMPLETED!")
    print("Files generated:")
    print("- kfold_results_comprehensive.png")
    print("- kfold_confusion_matrices.png")
    print("- kfold_average_confusion_matrix.png")
    print("- kfold_roc_analysis.png")
    print("- kfold_results_summary.csv")
    print("- kfold_thesis_report.txt")

    return evaluator, results_summary

if __name__ == "__main__":
    # Example configuration - adjust paths according to your setup
    TRAIN_DIR = "/content/dataset/train"  # Adjust to your path
    CLASS_NAMES = [
        'Lonchura leucogastroides',
        'Lonchura maja',
        'Lonchura punctulata',
        'Passer montanus',
        'Unknown'
    ]

    # Run enhanced k-fold evaluation
    evaluator, results = run_enhanced_kfold_evaluation(
        train_dir=TRAIN_DIR,
        class_names=CLASS_NAMES,
        epochs=20,  # Adjust based on your computational resources
        n_splits=5  # Standard 5-fold CV
    )
