import os
import sys
import shutil
import pathlib
import natsort
import numpy as np
import pandas as pd
import tensorflow as tf
import matplotlib.pyplot as plt
import seaborn as sns
import os
from google.colab import drive
from PIL import Image
from tqdm import tqdm
from tensorflow.keras.preprocessing import image
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Dropout, Flatten, Dense
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping

from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    confusion_matrix,
    classification_report
)

drive.mount('/content/drive')
!cp "/content/drive/MyDrive/Dataset.zip" "/content/"

# Unzip the dataset
!unzip -q '/content/Dataset.zip' -d '/content/'

# Define the main data directory
datasetDir = '/content/Dataset/'

# show amount of data for each diseases type
print("amount data of Lonchura leucogastroides : ", len(os.listdir(datasetDir + 'train/Lonchura leucogastroides')))
print("amount data of Lonchura maja : ", len(os.listdir(datasetDir + 'train/Lonchura maja')))
print("amount data of Lonchura punctulata: ", len(os.listdir(datasetDir + 'train/Lonchura punctulata')))
print("amount data of Passer montanus : ", len(os.listdir(datasetDir + 'train/Passer montanus')))
print("amount data of unknown spesies : ", len(os.listdir(datasetDir + 'train/Unknown')))
print("ammount data of testing directory : ", len(os.listdir(datasetDir + 'test')))

# Define bird_classes and num_classes based on the training directory structure
train_data_dir = os.path.join(datasetDir, 'train')
bird_classes = []
num_classes = 0

if os.path.exists(train_data_dir):
    # Get list of directories (classes) inside the train folder
    bird_classes = [d for d in os.listdir(train_data_dir) if os.path.isdir(os.path.join(train_data_dir, d))]
    num_classes = len(bird_classes)
    print(f"Detected {num_classes} classes: {bird_classes}")

    # show amount of data for each class in the training directory
    print("\nAmount of data for each class in the training directory:")
    for class_name in bird_classes:
        class_path = os.path.join(train_data_dir, class_name)
        if os.path.exists(class_path):
            num_images = len([f for f in os.listdir(class_path) if os.path.isfile(os.path.join(class_path, f))])
            print(f"amount data of {class_name} : {num_images}")
else:
    print(f"Error: Training data directory not found at {train_data_dir}")


# show amount of data in the testing directory
test_data_dir = os.path.join(datasetDir, 'test')
if os.path.exists(test_data_dir):
     # Count files directly in the test directory, assuming it contains images or subfolders with images
     # Adjust the logic here if the test directory has a different structure (e.g., subfolders for classes)
     total_test_images = len([f for f in os.listdir(test_data_dir) if os.path.isfile(os.path.join(test_data_dir, f))])
     print(f"\nAmount of data in testing directory : {total_test_images}")
else:
    print(f"\nWarning: Testing directory not found at {test_data_dir}")

# ===== DATA GENERATORS =====
# Data generator untuk training dengan augmentasi
train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,
    width_shift_range=0.1,
    height_shift_range=0.1,
    shear_range=0.1,
    zoom_range=0.15,
    horizontal_flip=True,
    vertical_flip=False,  # Tidak flip vertikal untuk burung
    fill_mode='nearest',
    validation_split=0.3  # 30% untuk validasi
)

# Data generator untuk validasi (hanya rescaling)
val_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3
)

# Batch size disesuaikan untuk CNN custom
batch_size = 16
target_size = (224, 224)

# Training generator - Pointing to the 'train' subdirectory
train_generator = train_datagen.flow_from_directory(
    r'/content/Dataset/train', # Corrected path with leading slash
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True
)

# Validation generator - Pointing to the 'train' subdirectory and using validation split
validation_generator = val_datagen.flow_from_directory(
    r'/content/Dataset/train', # Corrected path with leading slash
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False
)

# Update num_classes based on the generator
num_classes = train_generator.num_classes

print(f"\nData generators berhasil dibuat!")
print(f"Training samples: {train_generator.samples}")
print(f"Validation samples: {validation_generator.samples}")
print(f"Number of classes: {num_classes}")
print(f"Batch size: {batch_size}")
print(f"Target size: {target_size}")

# ===== CUSTOM CNN ARCHITECTURE =====
# Arsitektur CNN yang disesuaikan untuk klasifikasi burung
model = Sequential([
    # Explicitly define the input layer
    tf.keras.layers.Input(shape=(*target_size, 3)), # Use target_size defined earlier

    # Block 1
    Conv2D(32, (3, 3), activation='relu', padding='same'),
    Conv2D(32, (3, 3), activation='relu', padding='same'),
    MaxPooling2D((2, 2)),
    Dropout(0.25),

    # Block 2
    Conv2D(64, (3, 3), activation='relu', padding='same'),
    Conv2D(64, (3, 3), activation='relu', padding='same'),
    MaxPooling2D((2, 2)),
    Dropout(0.25),

    # Block 3
    Conv2D(128, (3, 3), activation='relu', padding='same'),
    Conv2D(128, (3, 3), activation='relu', padding='same'),
    MaxPooling2D((2, 2)),
    Dropout(0.25),

    # Block 4
    Conv2D(256, (3, 3), activation='relu', padding='same'),
    Conv2D(256, (3, 3), activation='relu', padding='same'),
    MaxPooling2D((2, 2)),
    Dropout(0.25),

    # Classifier
    Flatten(),
    Dense(512, activation='relu'),
    Dropout(0.5),
    Dense(256, activation='relu'),
    Dropout(0.5),
    Dense(num_classes, activation='softmax')
])

# Explicitly build the model after defining layers
# This is necessary before calling summary() or count_params()
model.build(input_shape=(None, *target_size, 3)) # Use None for batch size

# Compile model
model.compile(
    optimizer=Adam(learning_rate=0.001),
    loss='categorical_crossentropy',
    metrics=['accuracy']
)

# Display model summary
print("\nModel Architecture Summary:")
model.summary()

# Hitung total parameter
total_params = model.count_params()
print(f"\nTotal Parameters: {total_params:,}")
print(f"Model size estimate: ~{total_params * 4 / (1024*1024):.1f} MB")

# ===== TRAINING CONFIGURATION =====
# Buat direktori untuk menyimpan model
model_dir = './models'
os.makedirs(model_dir, exist_ok=True)

# Model checkpoint callback
checkpoint = ModelCheckpoint(
    filepath=os.path.join(model_dir, 'best_bird_cnn_model.h5'),
    monitor='val_accuracy',
    mode='max',
    save_best_only=True,
    save_weights_only=False,
    verbose=1
)

# Early stopping callback
early_stopping = EarlyStopping(
    monitor='val_loss',
    min_delta=0.001,
    patience=15,
    verbose=1,
    restore_best_weights=True
)

# Custom callback untuk stop training pada akurasi tertentu
class AccuracyThresholdCallback(tf.keras.callbacks.Callback):
    def __init__(self, threshold=0.95):
        super(AccuracyThresholdCallback, self).__init__()
        self.threshold = threshold

    def on_epoch_end(self, epoch, logs=None):
        if logs.get('val_accuracy') is not None and logs.get('val_accuracy') > self.threshold:
            print(f"\n🎯 Reached {self.threshold*100}% validation accuracy! Stopping training.")
            self.model.stop_training = True

accuracy_callback = AccuracyThresholdCallback(threshold=0.92)

# Learning rate scheduler
def scheduler(epoch, lr):
    if epoch < 10:
        return float(lr) # Ensure it returns a float
    else:
        return float(lr * tf.math.exp(-0.1)) # Convert the tensor result to a float

lr_scheduler = tf.keras.callbacks.LearningRateScheduler(scheduler, verbose=1)

# Combine all callbacks
callbacks = [checkpoint, early_stopping, accuracy_callback, lr_scheduler]

print("Training configuration ready!")
print(f"Model will be saved to: {os.path.join(model_dir, 'best_bird_cnn_model.h5')}")

# ===== MODEL TRAINING =====
# Calculate steps per epoch
steps_per_epoch = max(1, train_generator.samples // batch_size)
validation_steps = max(1, validation_generator.samples // batch_size)

# Train the model
history = model.fit(
    train_generator,
    steps_per_epoch=steps_per_epoch,
    epochs=50,
    validation_data=validation_generator,
    validation_steps=validation_steps,
    callbacks=callbacks,
    verbose=1
)

# ===== TRAINING HISTORY VISUALIZATION =====
print("📊 Visualizing training history...")

# Extract training history
acc = history.history['accuracy']
val_acc = history.history['val_accuracy']
loss = history.history['loss']
val_loss = history.history['val_loss']
epochs_range = range(len(acc))

# Create subplots
plt.figure(figsize=(15, 5))

# Accuracy plot
plt.subplot(1, 3, 1)
plt.plot(epochs_range, acc, label='Training Accuracy', linewidth=2)
plt.plot(epochs_range, val_acc, label='Validation Accuracy', linewidth=2)
plt.title('🎯 Training and Validation Accuracy', fontsize=14, fontweight='bold')
plt.xlabel('Epochs')
plt.ylabel('Accuracy')
plt.legend()
plt.grid(True, alpha=0.3)

# Loss plot
plt.subplot(1, 3, 2)
plt.plot(epochs_range, loss, label='Training Loss', linewidth=2)
plt.plot(epochs_range, val_loss, label='Validation Loss', linewidth=2)
plt.title('📉 Training and Validation Loss', fontsize=14, fontweight='bold')
plt.xlabel('Epochs')
plt.ylabel('Loss')
plt.legend()
plt.grid(True, alpha=0.3)

# Learning rate plot (if available)
plt.subplot(1, 3, 3)
if 'lr' in history.history:
    plt.plot(epochs_range, history.history['lr'], label='Learning Rate', linewidth=2, color='orange')
    plt.title('📈 Learning Rate Schedule', fontsize=14, fontweight='bold')
    plt.xlabel('Epochs')
    plt.ylabel('Learning Rate')
    plt.yscale('log')
    plt.legend()
    plt.grid(True, alpha=0.3)
else:
    # Show final metrics instead
    final_acc = acc[-1]
    final_val_acc = val_acc[-1]
    final_loss = loss[-1]
    final_val_loss = val_loss[-1]

    metrics_text = f"Final Metrics:\n\n"
    metrics_text += f"Training Accuracy: {final_acc:.4f}\n"
    metrics_text += f"Validation Accuracy: {final_val_acc:.4f}\n\n"
    metrics_text += f"Training Loss: {final_loss:.4f}\n"
    metrics_text += f"Validation Loss: {final_val_loss:.4f}"

    plt.text(0.1, 0.5, metrics_text, fontsize=12,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
             transform=plt.gca().transAxes)
    plt.title('📊 Final Training Metrics', fontsize=14, fontweight='bold')
    plt.axis('off')

plt.tight_layout()
plt.show()

# Save training history
results_dir = './results'
os.makedirs(results_dir, exist_ok=True)

# Save as CSV
history_df = pd.DataFrame(history.history)
history_df.to_csv(os.path.join(results_dir, 'training_history.csv'), index=False)

print(f"✅ Training history saved to: {os.path.join(results_dir, 'training_history.csv')}")

# ===== FEATURE MAP VISUALIZATION =====
# Explicitly build the model with the correct input shape
try:
    model.build(input_shape=(None, *target_size, 3))
    print("Model built successfully using model.build().")
except Exception as e:
    print(f"Error building model explicitly: {e}")
    # Fallback: try dummy call if explicit build fails (though it failed before)
    try:
        dummy_input = tf.zeros((1, *target_size, 3))
        _ = model(dummy_input)
        print("Model built successfully using dummy input call.")
    except Exception as e_dummy:
        print(f"Error building model with dummy input: {e_dummy}")


# Get indices of convolutional layers
conv_layer_indices = []
conv_layer_names = []

for i, layer in enumerate(model.layers):
    # Check if the layer is a Conv2D layer and if it has output defined (implies it's part of the graph)
    if isinstance(layer, Conv2D) and hasattr(layer, 'output'):
        conv_layer_indices.append(i)
        conv_layer_names.append(layer.name)
    elif isinstance(layer, Conv2D):
         print(f"Warning: Conv2D layer '{layer.name}' found but seems not connected or built properly.")


print(f"📋 Found {len(conv_layer_indices)} convolutional layers:")
for i, name in enumerate(conv_layer_names):
    print(f"   {i+1}. {name} (index: {conv_layer_indices[i]})")

# Ensure we have enough conv layers before proceeding
if len(conv_layer_indices) < 4:
    print("Not enough convolutional layers found (need at least 4) for feature map visualization.")
    # Skip feature map visualization if not enough layers
    sample_image = None # Prevent visualization attempt
    feature_model = None # Prevent creation of feature_model

# Create models to extract feature maps from first few conv layers, only if model is built and enough layers exist
if model.built and len(conv_layer_indices) >= 4:
    try:
        feature_layers = [model.layers[i].output for i in conv_layer_indices[:4]]  # First 4 conv layers
        feature_model = tf.keras.Model(inputs=model.input, outputs=feature_layers)
        print("Feature extraction model created successfully.")
    except Exception as e:
        print(f"Error creating feature extraction model: {e}")
        feature_model = None
else:
    feature_model = None
    if not model.built:
        print("Could not create feature extraction model because the main model is not built.")
    elif len(conv_layer_indices) < 4:
         print("Could not create feature extraction model because not enough convolutional layers were found.")


# Function to visualize feature maps
def visualize_feature_maps(model, img_path, layer_names, n_features=8):
    """Visualize feature maps from convolutional layers"""
    if model is None:
        print("Feature model is not available for visualization.")
        return

    # Load and preprocess image
    img = image.load_img(img_path, target_size=(224, 224))
    img_array = image.img_to_array(img)
    img_array = np.expand_dims(img_array, axis=0) / 255.0

    # Get feature maps
    try:
        feature_maps = model.predict(img_array)
    except Exception as e:
        print(f"Error during feature map prediction: {e}")
        return


    # Plot original image and feature maps
    n_layers = len(feature_maps)
    fig, axes = plt.subplots(n_layers + 1, n_features + 1, figsize=(20, 4 * (n_layers + 1)))

    # Show original image
    axes[0, 0].imshow(img)
    axes[0, 0].set_title('Original Image', fontweight='bold')
    axes[0, 0].axis('off')

    # Hide unused subplots in first row
    for j in range(1, n_features + 1):
        axes[0, j].axis('off')

    # Show layer info
    for i, (feature_map, layer_name) in enumerate(zip(feature_maps, layer_names)):
        # Show layer info
        axes[i + 1, 0].text(0.5, 0.5, f'{layer_name}\nShape: {feature_map.shape[1:]}',
                           ha='center', va='center', fontsize=10, fontweight='bold',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        axes[i + 1, 0].axis('off')

        # Show feature maps
        for j in range(min(n_features, feature_map.shape[-1])):
            # Ensure the feature map has expected dimensions before plotting
            if feature_map.ndim == 4: # (batch_size, height, width, channels)
                 axes[i + 1, j + 1].imshow(feature_map[0, :, :, j], cmap='viridis')
                 axes[i + 1, j + 1].set_title(f'Filter {j+1}', fontsize=8)
                 axes[i + 1, j + 1].axis('off')
            else:
                 print(f"Warning: Feature map for layer {layer_name}, filter {j+1} has unexpected shape {feature_map.shape}")
                 axes[i + 1, j + 1].axis('off')


    plt.suptitle(f'🔍 Feature Maps Visualization - {os.path.basename(img_path)}',
                 fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.show()

# Find a sample image for visualization
sample_image = None
# Assuming bird_classes and datasetDir are defined in previous cells
# Use datasetDir as clarified by the user
if 'bird_classes' in locals() and 'datasetDir' in locals() and datasetDir:
    # Look for an image in the 'train' subset within datasetDir
    train_data_dir = os.path.join(datasetDir, 'train')
    if os.path.exists(train_data_dir):
        for class_name in bird_classes:
            class_path = os.path.join(train_data_dir, class_name)
            if os.path.exists(class_path):
                image_files = [f for f in os.listdir(class_path)
                              if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))][:1]
                if image_files:
                    sample_image = os.path.join(class_path, image_files[0])
                    break
    else:
         print(f"Training data directory not found at {train_data_dir}")
else:
    print("Could not find 'bird_classes' or 'datasetDir'. Please ensure previous cells defining these are executed.")


if sample_image and feature_model is not None: # Check if feature_model was successfully created
    print(f"Visualizing feature maps for: {sample_image}")
    visualize_feature_maps(feature_model, sample_image, conv_layer_names[:4], n_features=6)
elif feature_model is None:
     print("Skipping feature map visualization because feature model could not be created.")
else:
    print("Could not create feature map visualization. Ensure a sample image was found and at least 4 convolutional layers exist and the model is built.")

# ===== K-FOLD CROSS-VALIDATION =====
print("⏳ Starting K-Fold Cross-Validation...")

from sklearn.model_selection import KFold
import numpy as np

# Define the number of folds
n_splits = 5 # You can adjust this number

# Prepare data for K-Fold
# Need to get file paths and labels from the training directory
train_data_dir = os.path.join(datasetDir, 'train') # Using datasetDir as clarified
if not os.path.exists(train_data_dir):
    print(f"Error: Training data directory not found at {train_data_dir}. Cannot perform cross-validation.")
else:
    # Get all image paths and their corresponding labels from the training directory
    all_image_paths = []
    all_image_labels = []
    print(f"Collecting image paths and labels from: {train_data_dir}")

    # Ensure bird_classes is defined and contains the class names
    if 'bird_classes' not in locals() or not bird_classes:
        print("Error: 'bird_classes' not defined. Please run the data configuration cell first.")
    else:
        class_names_list = bird_classes # Use the list of class names

        for class_name in class_names_list:
            class_path = os.path.join(train_data_dir, class_name)
            if os.path.exists(class_path):
                image_files = [os.path.join(class_path, f) for f in os.listdir(class_path)
                               if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
                all_image_paths.extend(image_files)
                all_image_labels.extend([class_name] * len(image_files)) # Assign label for each image

        if not all_image_paths:
             print("No images found in the training directory.")
        else:
            # Map string labels to integer indices
            label_to_index = {name: i for i, name in enumerate(class_names_list)}
            all_image_indices = np.array([label_to_index[label] for label in all_image_labels])
            all_image_paths = np.array(all_image_paths)

            print(f"Found {len(all_image_paths)} total training images.")

            # Initialize KFold
            kf = KFold(n_splits=n_splits, shuffle=True, random_state=42) # Added random_state for reproducibility

            # Store results from each fold
            fold_accuracies = []
            fold_losses = []

            print(f"\nRunning {n_splits}-Fold Cross-Validation...")

            # Loop through each fold
            for fold, (train_index, val_index) in enumerate(kf.split(all_image_paths, all_image_indices)):
                print(f"\n--- Fold {fold+1}/{n_splits} ---")

                # Get train and validation data for this fold
                X_train_fold, X_val_fold = all_image_paths[train_index], all_image_paths[val_index]
                y_train_fold_indices, y_val_fold_indices = all_image_indices[train_index], all_image_indices[val_index]

                # Convert integer labels to one-hot encoded (needed for categorical_crossentropy)
                y_train_fold_one_hot = tf.keras.utils.to_categorical(y_train_fold_indices, num_classes=num_classes)
                y_val_fold_one_hot = tf.keras.utils.to_categorical(y_val_fold_indices, num_classes=num_classes)


                # Create Data Generators for this fold
                # Note: We need to create new generators for each fold to use the correct subsets of data
                fold_train_datagen = ImageDataGenerator(
                    rescale=1./255,
                    rotation_range=15,
                    width_shift_range=0.1,
                    height_shift_range=0.1,
                    shear_range=0.1,
                    zoom_range=0.15,
                    horizontal_flip=True,
                    vertical_flip=False,
                    fill_mode='nearest'
                )

                fold_val_datagen = ImageDataGenerator(rescale=1./255)

                # Use flow_from_dataframe or similar if direct pathing is complex with KFold indices
                # A simple approach is to load images manually for smaller datasets or use tf.data pipeline for large ones
                # Given the current structure, let's try a simplified approach by passing arrays directly
                # This requires loading images into memory, which might be an issue for very large datasets.
                # For large datasets, a tf.data pipeline would be more suitable, but requires significant code change.
                # Let's assume for now the dataset size allows loading subsets into memory for each fold.

                print("Loading images for the current fold...")
                X_train_images_fold = np.array([image.img_to_array(image.load_img(p, target_size=target_size)) for p in X_train_fold])
                X_val_images_fold = np.array([image.img_to_array(image.load_img(p, target_size=target_size)) for p in X_val_fold])

                # Rescale images
                X_train_images_fold /= 255.0
                X_val_images_fold /= 255.0

                # Re-create the model architecture for each fold to ensure a fresh start
                # (Weights are not shared between folds)
                # You might want to save the model architecture separately and load it here
                # For now, let's assume the model definition is available globally or can be recreated.
                # (This assumes the model architecture code cell has been run)

                # Recreate the model using the Sequential definition from cell REo-2V8bBKGL
                # This is a placeholder - ideally, define a function to create the model
                print("Recreating model for the current fold...")
                try:
                    fold_model = Sequential([
                        # Block 1
                        Conv2D(32, (3, 3), activation='relu', padding='same', input_shape=(*target_size, 3)),
                        Conv2D(32, (3, 3), activation='relu', padding='same'),
                        MaxPooling2D((2, 2)),
                        Dropout(0.25),

                        # Block 2
                        Conv2D(64, (3, 3), activation='relu', padding='same'),
                        Conv2D(64, (3, 3), activation='relu', padding='same'),
                        MaxPooling2D((2, 2)),
                        Dropout(0.25),

                        # Block 3
                        Conv2D(128, (3, 3), activation='relu', padding='same'),
                        Conv2D(128, (3, 3), activation='relu', padding='same'),
                        MaxPooling2D((2, 2)),
                        Dropout(0.25),

                        # Block 4
                        Conv2D(256, (3, 3), activation='relu', padding='same'),
                        Conv2D(256, (3, 3), activation='relu', padding='same'),
                        MaxPooling2D((2, 2)),
                        Dropout(0.25),

                        # Classifier
                        Flatten(),
                        Dense(512, activation='relu'),
                        Dropout(0.5),
                        Dense(256, activation='relu'),
                        Dropout(0.5),
                        Dense(num_classes, activation='softmax') # Use num_classes defined earlier
                    ])

                    # Compile model
                    fold_model.compile(
                        optimizer=Adam(learning_rate=0.001),
                        loss='categorical_crossentropy',
                        metrics=['accuracy']
                    )
                    print("Model recreated and compiled.")

                except Exception as e:
                    print(f"Error recreating model for fold {fold+1}: {e}")
                    continue # Skip this fold if model recreation fails


                # Train the model on the fold's training data
                print("Training model for the current fold...")
                # Use fit() with the data arrays
                history_fold = fold_model.fit(
                    fold_train_datagen.flow(X_train_images_fold, y_train_fold_one_hot, batch_size=batch_size),
                    epochs=10, # Reduced epochs for faster CV, adjust as needed
                    validation_data=fold_val_datagen.flow(X_val_images_fold, y_val_fold_one_hot, batch_size=batch_size),
                    verbose=0 # Set to 1 to see training progress per epoch
                )
                print(f"Training finished for fold {fold+1}.")

                # Evaluate the model on the fold's validation data
                print(f"Evaluating model for fold {fold+1}...")
                loss_fold, accuracy_fold = fold_model.evaluate(fold_val_datagen.flow(X_val_images_fold, y_val_fold_one_hot, batch_size=batch_size), verbose=0)
                print(f"Fold {fold+1} - Loss: {loss_fold:.4f}, Accuracy: {accuracy_fold:.4f}")

                fold_losses.append(loss_fold)
                fold_accuracies.append(accuracy_fold)

                # Optional: Clear session to free memory, especially for many folds
                tf.keras.backend.clear_session()


            # Report average results across all folds
            print("\n--- Cross-Validation Summary ---")
            print(f"Average Validation Accuracy: {np.mean(fold_accuracies):.4f} (+/- {np.std(fold_accuracies):.4f})")
            print(f"Average Validation Loss: {np.mean(fold_losses):.4f} (+/- {np.std(fold_losses):.4f})")
            print("✅ K-Fold Cross-Validation finished.")