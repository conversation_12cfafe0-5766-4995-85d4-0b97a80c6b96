"""
KODE SIAP PAKAI UNTUK NOTEBOOK BIRD CLASSIFICATION
Copy-paste kode ini ke dalam cell notebook Anda sesuai urutan
"""

# ============================================================================
# CELL 1: IMPORT LIBRARIES TAMBAHAN (Tambahkan ke cell import yang sudah ada)
# ============================================================================

# Tambahkan import ini ke cell import libraries yang sudah ada
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_curve, auc
)
from sklearn.preprocessing import label_binarize
from sklearn.model_selection import StratifiedKFold
import pandas as pd
import seaborn as sns
from scipy import stats

# ============================================================================
# CELL 2: EXPLORATORY DATA ANALYSIS (EDA) - WAJIB UNTUK SKRIPSI
# ============================================================================

# === ANALISIS DISTRIBUSI DATASET ===
print("=== EXPLORATORY DATA ANALYSIS (EDA) ===")

# Hitung distribusi data per kelas
def analyze_dataset_distribution(train_dir, test_dir, class_names):
    """Analisis distribusi dataset untuk skripsi"""
    
    print("📊 Menganalisis distribusi dataset...")
    
    # Training data distribution
    train_counts = {}
    for class_name in class_names:
        class_path = os.path.join(train_dir, class_name)
        if os.path.exists(class_path):
            train_counts[class_name] = len([f for f in os.listdir(class_path) 
                                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
    
    # Testing data distribution  
    test_counts = {}
    for class_name in class_names:
        class_path = os.path.join(test_dir, class_name)
        if os.path.exists(class_path):
            test_counts[class_name] = len([f for f in os.listdir(class_path) 
                                         if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
    
    # Print statistics
    total_train = sum(train_counts.values())
    total_test = sum(test_counts.values())
    total_all = total_train + total_test
    
    print(f"\n📈 STATISTIK DATASET:")
    print(f"Total Training Images: {total_train}")
    print(f"Total Testing Images: {total_test}")
    print(f"Total Dataset: {total_all}")
    
    print(f"\n📋 DISTRIBUSI PER KELAS:")
    for class_name in class_names:
        train_count = train_counts.get(class_name, 0)
        test_count = test_counts.get(class_name, 0)
        total_count = train_count + test_count
        percentage = (total_count / total_all) * 100 if total_all > 0 else 0
        print(f"{class_name}:")
        print(f"  Training: {train_count} | Testing: {test_count} | Total: {total_count} ({percentage:.1f}%)")
    
    # Visualisasi distribusi
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Training distribution
    bars1 = ax1.bar(range(len(train_counts)), list(train_counts.values()), 
                    color='skyblue', alpha=0.8, edgecolor='navy')
    ax1.set_title('Distribusi Data Training per Kelas', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Kelas Burung')
    ax1.set_ylabel('Jumlah Gambar')
    ax1.set_xticks(range(len(train_counts)))
    ax1.set_xticklabels(list(train_counts.keys()), rotation=45, ha='right')
    ax1.grid(True, alpha=0.3)
    
    # Add value labels
    for bar, count in zip(bars1, train_counts.values()):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + max(train_counts.values()) * 0.01,
                str(count), ha='center', va='bottom', fontweight='bold')
    
    # Testing distribution
    bars2 = ax2.bar(range(len(test_counts)), list(test_counts.values()), 
                    color='lightcoral', alpha=0.8, edgecolor='darkred')
    ax2.set_title('Distribusi Data Testing per Kelas', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Kelas Burung')
    ax2.set_ylabel('Jumlah Gambar')
    ax2.set_xticks(range(len(test_counts)))
    ax2.set_xticklabels(list(test_counts.keys()), rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    # Add value labels
    for bar, count in zip(bars2, test_counts.values()):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + max(test_counts.values()) * 0.01,
                str(count), ha='center', va='bottom', fontweight='bold')
    
    # Combined distribution
    combined_counts = [train_counts.get(name, 0) + test_counts.get(name, 0) for name in class_names]
    bars3 = ax3.bar(range(len(class_names)), combined_counts, 
                    color='lightgreen', alpha=0.8, edgecolor='darkgreen')
    ax3.set_title('Total Distribusi Dataset', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Kelas Burung')
    ax3.set_ylabel('Total Gambar')
    ax3.set_xticks(range(len(class_names)))
    ax3.set_xticklabels(class_names, rotation=45, ha='right')
    ax3.grid(True, alpha=0.3)
    
    # Add value labels
    for bar, count in zip(bars3, combined_counts):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + max(combined_counts) * 0.01,
                str(count), ha='center', va='bottom', fontweight='bold')
    
    # Class balance analysis
    percentages = [(count/total_all)*100 for count in combined_counts]
    colors_pie = plt.cm.Set3(np.linspace(0, 1, len(class_names)))
    
    wedges, texts, autotexts = ax4.pie(percentages, labels=class_names, autopct='%1.1f%%',
                                      colors=colors_pie, startangle=90)
    ax4.set_title('Proporsi Kelas dalam Dataset', fontsize=14, fontweight='bold')
    
    # Make percentage text bold
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
    
    plt.tight_layout()
    plt.savefig('dataset_eda_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return train_counts, test_counts

# Jalankan analisis EDA
train_counts, test_counts = analyze_dataset_distribution(
    train_dir='/content/dataset/train',  # Sesuaikan dengan path Anda
    test_dir='/content/dataset/test',    # Sesuaikan dengan path Anda
    class_names=['Lonchura leucogastroides', 'Lonchura maja', 'Lonchura punctulata', 'Passer montanus', 'Unknown']
)

# ============================================================================
# CELL 3: SAMPLE IMAGES VISUALIZATION - WAJIB UNTUK SKRIPSI
# ============================================================================

def show_sample_images_per_class(train_dir, class_names, samples_per_class=3):
    """Tampilkan sample gambar dari setiap kelas"""
    
    print("🖼️ Menampilkan sample gambar per kelas...")
    
    n_classes = len(class_names)
    fig, axes = plt.subplots(n_classes, samples_per_class, figsize=(15, 3 * n_classes))
    
    if n_classes == 1:
        axes = axes.reshape(1, -1)
    
    for class_idx, class_name in enumerate(class_names):
        class_path = os.path.join(train_dir, class_name)
        
        if os.path.exists(class_path):
            image_files = [f for f in os.listdir(class_path) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            
            for sample_idx in range(min(samples_per_class, len(image_files))):
                if samples_per_class == 1:
                    ax = axes[class_idx]
                else:
                    ax = axes[class_idx, sample_idx]
                
                try:
                    img_path = os.path.join(class_path, image_files[sample_idx])
                    img = plt.imread(img_path)
                    ax.imshow(img)
                    
                    if sample_idx == 0:  # Only show class name on first image
                        ax.set_title(f'{class_name}', fontsize=12, fontweight='bold')
                    else:
                        ax.set_title(f'Sample {sample_idx + 1}', fontsize=10)
                    
                    ax.axis('off')
                    
                except Exception as e:
                    ax.text(0.5, 0.5, f'Error\nloading\nimage', ha='center', va='center',
                           transform=ax.transAxes, fontsize=10)
                    ax.axis('off')
            
            # Hide unused subplots for this class
            for sample_idx in range(len(image_files), samples_per_class):
                if samples_per_class > 1:
                    axes[class_idx, sample_idx].axis('off')
    
    plt.suptitle('Sample Gambar per Kelas Burung', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('sample_images_per_class.png', dpi=300, bbox_inches='tight')
    plt.show()

# Jalankan visualisasi sample images
show_sample_images_per_class(
    train_dir='/content/dataset/train',  # Sesuaikan dengan path Anda
    class_names=['Lonchura leucogastroides', 'Lonchura maja', 'Lonchura punctulata', 'Passer montanus', 'Unknown'],
    samples_per_class=3
)

# ============================================================================
# CELL 4: ENHANCED TRAINING HISTORY VISUALIZATION - SUDAH ADA, TINGKATKAN
# ============================================================================

# Ganti cell training history yang sudah ada dengan kode ini:
def plot_enhanced_training_history(history):
    """Plot training history yang lebih komprehensif untuk skripsi"""
    
    print("📊 Visualizing enhanced training history...")
    
    # Extract data
    acc = history.history['accuracy']
    val_acc = history.history['val_accuracy']
    loss = history.history['loss']
    val_loss = history.history['val_loss']
    epochs_range = range(1, len(acc) + 1)
    
    # Create comprehensive plots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Accuracy plot
    ax1.plot(epochs_range, acc, 'b-', linewidth=2, label='Training Accuracy', marker='o', markersize=4)
    ax1.plot(epochs_range, val_acc, 'r-', linewidth=2, label='Validation Accuracy', marker='s', markersize=4)
    ax1.set_title('🎯 Model Accuracy', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Accuracy')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim([0, 1])
    
    # Loss plot
    ax2.plot(epochs_range, loss, 'b-', linewidth=2, label='Training Loss', marker='o', markersize=4)
    ax2.plot(epochs_range, val_loss, 'r-', linewidth=2, label='Validation Loss', marker='s', markersize=4)
    ax2.set_title('📉 Model Loss', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Overfitting analysis
    acc_diff = np.array(acc) - np.array(val_acc)
    ax3.plot(epochs_range, acc_diff, 'g-', linewidth=2, label='Training - Validation', marker='d', markersize=4)
    ax3.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax3.set_title('📈 Overfitting Analysis', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Accuracy Difference')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Training summary
    final_train_acc = acc[-1]
    final_val_acc = val_acc[-1]
    best_val_acc = max(val_acc)
    best_epoch = val_acc.index(best_val_acc) + 1
    overfitting_gap = final_train_acc - final_val_acc
    
    summary_text = f"""TRAINING SUMMARY:
    
Final Training Accuracy: {final_train_acc:.4f}
Final Validation Accuracy: {final_val_acc:.4f}
Best Validation Accuracy: {best_val_acc:.4f}
Best Epoch: {best_epoch}
Overfitting Gap: {overfitting_gap:.4f}

INTERPRETATION:
{'✅ Good generalization' if overfitting_gap < 0.1 else '⚠️ Potential overfitting'}
{'✅ Stable training' if abs(val_acc[-1] - val_acc[-5]) < 0.02 else '⚠️ Unstable convergence'}"""
    
    ax4.text(0.1, 0.5, summary_text, transform=ax4.transAxes, fontsize=11,
             verticalalignment='center', 
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    ax4.set_title('📋 Training Summary', fontsize=14, fontweight='bold')
    ax4.axis('off')
    
    plt.tight_layout()
    plt.savefig('enhanced_training_history.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print detailed summary
    print(f"\n📊 TRAINING RESULTS SUMMARY:")
    print(f"Training completed in {len(epochs_range)} epochs")
    print(f"Best validation accuracy: {best_val_acc:.4f} at epoch {best_epoch}")
    print(f"Final training accuracy: {final_train_acc:.4f}")
    print(f"Final validation accuracy: {final_val_acc:.4f}")
    print(f"Overfitting gap: {overfitting_gap:.4f}")
    
    return {
        'final_train_acc': final_train_acc,
        'final_val_acc': final_val_acc,
        'best_val_acc': best_val_acc,
        'best_epoch': best_epoch,
        'overfitting_gap': overfitting_gap
    }

# Jalankan enhanced training history (ganti yang sudah ada)
training_summary = plot_enhanced_training_history(history)

# ============================================================================
# CELL 5: COMPREHENSIVE MODEL EVALUATION - WAJIB UNTUK SKRIPSI
# ============================================================================

def comprehensive_model_evaluation(model, test_generator, class_names):
    """Evaluasi model yang komprehensif untuk skripsi"""
    
    print("=== COMPREHENSIVE MODEL EVALUATION ===")
    
    # Load best model
    model = tf.keras.models.load_model('./models/best_bird_cnn_model.h5')
    
    # Get predictions
    print("🔮 Melakukan prediksi pada test set...")
    test_generator.reset()  # Reset generator
    predictions = model.predict(test_generator, verbose=1)
    predicted_classes = np.argmax(predictions, axis=1)
    true_classes = test_generator.classes
    
    # Calculate comprehensive metrics
    accuracy = accuracy_score(true_classes, predicted_classes)
    precision_macro = precision_score(true_classes, predicted_classes, average='macro', zero_division=0)
    recall_macro = recall_score(true_classes, predicted_classes, average='macro', zero_division=0)
    f1_macro = f1_score(true_classes, predicted_classes, average='macro', zero_division=0)
    
    precision_weighted = precision_score(true_classes, predicted_classes, average='weighted', zero_division=0)
    recall_weighted = recall_score(true_classes, predicted_classes, average='weighted', zero_division=0)
    f1_weighted = f1_score(true_classes, predicted_classes, average='weighted', zero_division=0)
    
    # Print overall metrics
    print(f"\n📊 OVERALL PERFORMANCE METRICS:")
    print(f"Overall Accuracy: {accuracy:.4f}")
    print(f"Macro-average Precision: {precision_macro:.4f}")
    print(f"Macro-average Recall: {recall_macro:.4f}")
    print(f"Macro-average F1-score: {f1_macro:.4f}")
    print(f"Weighted-average Precision: {precision_weighted:.4f}")
    print(f"Weighted-average Recall: {recall_weighted:.4f}")
    print(f"Weighted-average F1-score: {f1_weighted:.4f}")
    
    # Detailed classification report
    print(f"\n📋 DETAILED CLASSIFICATION REPORT:")
    cls_report = classification_report(true_classes, predicted_classes, 
                                     target_names=class_names, digits=4)
    print(cls_report)
    
    # Per-class accuracy analysis
    print(f"\n🎯 PER-CLASS ACCURACY ANALYSIS:")
    cm = confusion_matrix(true_classes, predicted_classes)
    class_accuracies = cm.diagonal() / cm.sum(axis=1)
    
    for i, (class_name, acc) in enumerate(zip(class_names, class_accuracies)):
        total_samples = cm.sum(axis=1)[i]
        correct_predictions = cm.diagonal()[i]
        print(f"{class_name}: {acc:.4f} ({correct_predictions}/{total_samples})")
    
    # Model efficiency
    model_size_mb = model.count_params() * 4 / (1024 * 1024)
    print(f"\n⚙️ MODEL EFFICIENCY:")
    print(f"Total Parameters: {model.count_params():,}")
    print(f"Model Size: {model_size_mb:.2f} MB")
    
    return {
        'predictions': predictions,
        'predicted_classes': predicted_classes,
        'true_classes': true_classes,
        'accuracy': accuracy,
        'precision_macro': precision_macro,
        'recall_macro': recall_macro,
        'f1_macro': f1_macro,
        'class_accuracies': class_accuracies,
        'confusion_matrix': cm,
        'classification_report': cls_report,
        'model_size_mb': model_size_mb
    }

# Setup test generator (sesuaikan dengan struktur data Anda)
test_datagen = ImageDataGenerator(rescale=1./255)
test_generator = test_datagen.flow_from_directory(
    '/content/dataset/test',  # Sesuaikan dengan path Anda
    target_size=(224, 224),
    batch_size=16,
    class_mode='categorical',
    shuffle=False
)

# Jalankan evaluasi komprehensif
evaluation_results = comprehensive_model_evaluation(
    model=model,
    test_generator=test_generator,
    class_names=['Lonchura leucogastroides', 'Lonchura maja', 'Lonchura punctulata', 'Passer montanus', 'Unknown']
)

# ============================================================================
# CELL 6: CONFUSION MATRIX VISUALIZATION - WAJIB UNTUK SKRIPSI
# ============================================================================

def plot_enhanced_confusion_matrix(true_classes, predicted_classes, class_names):
    """Plot confusion matrix yang enhanced untuk skripsi"""

    print("📊 Creating enhanced confusion matrix...")

    cm = confusion_matrix(true_classes, predicted_classes)
    cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100

    # Create figure with two subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))

    # Confusion matrix with counts
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names,
                cbar_kws={'label': 'Count'}, ax=ax1, annot_kws={'size': 12})
    ax1.set_title('🔢 Confusion Matrix (Counts)', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Predicted Label', fontsize=12)
    ax1.set_ylabel('True Label', fontsize=12)

    # Confusion matrix with percentages
    sns.heatmap(cm_percent, annot=True, fmt='.1f', cmap='Reds',
                xticklabels=class_names, yticklabels=class_names,
                cbar_kws={'label': 'Percentage (%)'}, ax=ax2, annot_kws={'size': 12})
    ax2.set_title('📊 Confusion Matrix (Percentages)', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Predicted Label', fontsize=12)
    ax2.set_ylabel('True Label', fontsize=12)

    plt.tight_layout()
    plt.savefig('enhanced_confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()

    # Analysis of confusion matrix
    print(f"\n🔍 CONFUSION MATRIX ANALYSIS:")

    # Calculate per-class accuracy
    class_accuracies = cm.diagonal() / cm.sum(axis=1)
    print(f"Per-class Accuracy:")
    for i, (class_name, acc) in enumerate(zip(class_names, class_accuracies)):
        print(f"  {class_name}: {acc:.4f}")

    # Find most confused classes
    cm_no_diag = cm.copy()
    np.fill_diagonal(cm_no_diag, 0)
    max_confusion_idx = np.unravel_index(np.argmax(cm_no_diag), cm_no_diag.shape)
    max_confusion_count = cm_no_diag[max_confusion_idx]

    if max_confusion_count > 0:
        print(f"\nMost confused classes:")
        print(f"  {class_names[max_confusion_idx[0]]} → {class_names[max_confusion_idx[1]]}: {max_confusion_count} cases")

    return cm, cm_percent

# Jalankan confusion matrix analysis
cm, cm_percent = plot_enhanced_confusion_matrix(
    evaluation_results['true_classes'],
    evaluation_results['predicted_classes'],
    ['Lonchura leucogastroides', 'Lonchura maja', 'Lonchura punctulata', 'Passer montanus', 'Unknown']
)

# ============================================================================
# CELL 7: ROC CURVE ANALYSIS - WAJIB UNTUK SKRIPSI
# ============================================================================

def plot_comprehensive_roc_curves(true_classes, predictions, class_names):
    """Plot ROC curves yang komprehensif untuk skripsi"""

    print("📈 Creating comprehensive ROC curve analysis...")

    # Binarize labels for multi-class ROC
    y_test_binarized = label_binarize(true_classes, classes=range(len(class_names)))
    n_classes = len(class_names)

    # Compute ROC curve and AUC for each class
    fpr = {}
    tpr = {}
    roc_auc = {}

    for i in range(n_classes):
        fpr[i], tpr[i], _ = roc_curve(y_test_binarized[:, i], predictions[:, i])
        roc_auc[i] = auc(fpr[i], tpr[i])

    # Compute micro-average ROC curve and AUC
    fpr["micro"], tpr["micro"], _ = roc_curve(y_test_binarized.ravel(), predictions.ravel())
    roc_auc["micro"] = auc(fpr["micro"], tpr["micro"])

    # Compute macro-average ROC curve and AUC
    all_fpr = np.unique(np.concatenate([fpr[i] for i in range(n_classes)]))
    mean_tpr = np.zeros_like(all_fpr)
    for i in range(n_classes):
        mean_tpr += np.interp(all_fpr, fpr[i], tpr[i])
    mean_tpr /= n_classes

    fpr["macro"] = all_fpr
    tpr["macro"] = mean_tpr
    roc_auc["macro"] = auc(fpr["macro"], tpr["macro"])

    # Create comprehensive ROC plot
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))

    # ROC curves for all classes
    colors = ['blue', 'red', 'green', 'orange', 'purple']

    for i in range(n_classes):
        color = colors[i % len(colors)]
        ax1.plot(fpr[i], tpr[i], color=color, linewidth=2,
                label=f'{class_names[i]} (AUC = {roc_auc[i]:.4f})')

    # Plot micro and macro averages
    ax1.plot(fpr["micro"], tpr["micro"], color='deeppink', linestyle=':', linewidth=3,
            label=f'Micro-average (AUC = {roc_auc["micro"]:.4f})')
    ax1.plot(fpr["macro"], tpr["macro"], color='navy', linestyle=':', linewidth=3,
            label=f'Macro-average (AUC = {roc_auc["macro"]:.4f})')

    # Plot random classifier
    ax1.plot([0, 1], [0, 1], 'k--', linewidth=2, label='Random Classifier (AUC = 0.5000)')

    ax1.set_xlim([0.0, 1.0])
    ax1.set_ylim([0.0, 1.05])
    ax1.set_xlabel('False Positive Rate', fontsize=14)
    ax1.set_ylabel('True Positive Rate', fontsize=14)
    ax1.set_title('🎯 Multi-class ROC Curves', fontsize=16, fontweight='bold')
    ax1.legend(loc="lower right", fontsize=10)
    ax1.grid(True, alpha=0.3)

    # AUC scores bar plot
    class_auc_scores = [roc_auc[i] for i in range(n_classes)]
    bars = ax2.bar(range(n_classes), class_auc_scores,
                   color=colors[:n_classes], alpha=0.7, edgecolor='black')

    ax2.set_title('📊 AUC Scores per Class', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Bird Classes', fontsize=12)
    ax2.set_ylabel('AUC Score', fontsize=12)
    ax2.set_xticks(range(n_classes))
    ax2.set_xticklabels(class_names, rotation=45, ha='right')
    ax2.set_ylim([0, 1])
    ax2.grid(True, alpha=0.3)

    # Add value labels on bars
    for bar, auc_score in zip(bars, class_auc_scores):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{auc_score:.3f}', ha='center', va='bottom', fontweight='bold')

    # Add macro-average line
    ax2.axhline(y=roc_auc["macro"], color='navy', linestyle='--', linewidth=2,
               label=f'Macro-average: {roc_auc["macro"]:.4f}')
    ax2.legend()

    plt.tight_layout()
    plt.savefig('comprehensive_roc_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

    # Print AUC summary
    print(f"\n📈 AUC SCORES SUMMARY:")
    for i, class_name in enumerate(class_names):
        print(f"{class_name}: {roc_auc[i]:.4f}")

    print(f"\nMicro-average AUC: {roc_auc['micro']:.4f}")
    print(f"Macro-average AUC: {roc_auc['macro']:.4f}")

    # Performance interpretation
    macro_auc = roc_auc['macro']
    if macro_auc >= 0.9:
        performance = "Excellent (≥0.9)"
    elif macro_auc >= 0.8:
        performance = "Good (0.8-0.9)"
    elif macro_auc >= 0.7:
        performance = "Fair (0.7-0.8)"
    else:
        performance = "Poor (<0.7)"

    print(f"\n🎯 OVERALL MODEL PERFORMANCE: {performance}")

    return roc_auc, fpr, tpr

# Jalankan ROC curve analysis
roc_results, fpr_results, tpr_results = plot_comprehensive_roc_curves(
    evaluation_results['true_classes'],
    evaluation_results['predictions'],
    ['Lonchura leucogastroides', 'Lonchura maja', 'Lonchura punctulata', 'Passer montanus', 'Unknown']
)

# ============================================================================
# CELL 8: PERFORMANCE SUMMARY TABLE - WAJIB UNTUK SKRIPSI
# ============================================================================

def create_comprehensive_performance_summary(evaluation_results, roc_results, training_summary):
    """Buat ringkasan performa yang komprehensif untuk skripsi"""

    print("📋 Creating comprehensive performance summary...")

    # Extract metrics
    accuracy = evaluation_results['accuracy']
    precision = evaluation_results['precision_macro']
    recall = evaluation_results['recall_macro']
    f1 = evaluation_results['f1_macro']
    class_accuracies = evaluation_results['class_accuracies']
    model_size_mb = evaluation_results['model_size_mb']

    # Create summary table
    summary_data = {
        'Metric': [
            'Overall Accuracy',
            'Macro-avg Precision',
            'Macro-avg Recall',
            'Macro-avg F1-Score',
            'Macro-avg AUC',
            'Micro-avg AUC',
            'Best Class Accuracy',
            'Worst Class Accuracy',
            'Model Size (MB)',
            'Total Parameters',
            'Best Validation Accuracy',
            'Overfitting Gap'
        ],
        'Value': [
            f'{accuracy:.4f}',
            f'{precision:.4f}',
            f'{recall:.4f}',
            f'{f1:.4f}',
            f'{roc_results["macro"]:.4f}',
            f'{roc_results["micro"]:.4f}',
            f'{max(class_accuracies):.4f}',
            f'{min(class_accuracies):.4f}',
            f'{model_size_mb:.2f}',
            f'{model.count_params():,}',
            f'{training_summary["best_val_acc"]:.4f}',
            f'{training_summary["overfitting_gap"]:.4f}'
        ]
    }

    df_summary = pd.DataFrame(summary_data)

    print("\n📊 COMPREHENSIVE PERFORMANCE SUMMARY:")
    print("=" * 60)
    print(df_summary.to_string(index=False))

    # Save to CSV for thesis documentation
    df_summary.to_csv('bird_classification_performance_summary.csv', index=False)
    print(f"\n💾 Performance summary saved to: bird_classification_performance_summary.csv")

    # Create performance visualization
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # Per-class accuracy
    class_names_short = ['L.leucogastroides', 'L.maja', 'L.punctulata', 'P.montanus', 'Unknown']
    bars1 = ax1.bar(range(len(class_names_short)), class_accuracies,
                   color='skyblue', alpha=0.8, edgecolor='navy')
    ax1.set_title('🎯 Per-Class Accuracy', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Bird Classes')
    ax1.set_ylabel('Accuracy')
    ax1.set_xticks(range(len(class_names_short)))
    ax1.set_xticklabels(class_names_short, rotation=45, ha='right')
    ax1.set_ylim([0, 1])
    ax1.grid(True, alpha=0.3)

    # Add value labels
    for bar, acc in zip(bars1, class_accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')

    # Per-class AUC scores
    class_auc_scores = [roc_results[i] for i in range(len(class_names))]
    bars2 = ax2.bar(range(len(class_names_short)), class_auc_scores,
                   color='lightcoral', alpha=0.8, edgecolor='darkred')
    ax2.set_title('📈 Per-Class AUC Scores', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Bird Classes')
    ax2.set_ylabel('AUC Score')
    ax2.set_xticks(range(len(class_names_short)))
    ax2.set_xticklabels(class_names_short, rotation=45, ha='right')
    ax2.set_ylim([0, 1])
    ax2.grid(True, alpha=0.3)

    # Add value labels
    for bar, auc_score in zip(bars2, class_auc_scores):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{auc_score:.3f}', ha='center', va='bottom', fontweight='bold')

    # Overall metrics radar chart
    metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'AUC']
    values = [accuracy, precision, recall, f1, roc_results['macro']]

    bars3 = ax3.bar(metrics, values, color=['gold', 'lightgreen', 'lightblue', 'plum', 'orange'],
                   alpha=0.8, edgecolor='black')
    ax3.set_title('📊 Overall Performance Metrics', fontsize=14, fontweight='bold')
    ax3.set_ylabel('Score')
    ax3.set_ylim([0, 1])
    ax3.grid(True, alpha=0.3)

    # Add value labels
    for bar, value in zip(bars3, values):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

    # Model complexity and performance trade-off
    complexity_data = {
        'Metric': ['Parameters (M)', 'Size (MB)', 'Accuracy', 'AUC'],
        'Value': [model.count_params()/1e6, model_size_mb, accuracy, roc_results['macro']]
    }

    ax4.text(0.1, 0.7, f"""MODEL PERFORMANCE SUMMARY:

✅ Overall Accuracy: {accuracy:.4f}
✅ Macro-average AUC: {roc_results['macro']:.4f}
✅ Best Class Accuracy: {max(class_accuracies):.4f}
⚠️ Worst Class Accuracy: {min(class_accuracies):.4f}

MODEL COMPLEXITY:
📊 Parameters: {model.count_params()/1e6:.1f}M
💾 Size: {model_size_mb:.1f} MB

TRAINING PERFORMANCE:
🎯 Best Val Accuracy: {training_summary['best_val_acc']:.4f}
📈 Overfitting Gap: {training_summary['overfitting_gap']:.4f}""",
             transform=ax4.transAxes, fontsize=11, verticalalignment='top',
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))

    ax4.set_title('📋 Model Summary Report', fontsize=14, fontweight='bold')
    ax4.axis('off')

    plt.tight_layout()
    plt.savefig('comprehensive_performance_summary.png', dpi=300, bbox_inches='tight')
    plt.show()

    return df_summary

# Jalankan performance summary
performance_summary = create_comprehensive_performance_summary(
    evaluation_results, roc_results, training_summary
)

# ============================================================================
# CELL 9: FINAL THESIS REPORT GENERATION - WAJIB UNTUK SKRIPSI
# ============================================================================

def generate_final_thesis_report(evaluation_results, roc_results, training_summary,
                                train_counts, test_counts, class_names):
    """Generate laporan final untuk dokumentasi skripsi"""

    print("📝 Generating final thesis report...")

    # Create comprehensive report
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("LAPORAN EVALUASI BIRD CLASSIFICATION CNN - UNTUK SKRIPSI")
    report_lines.append("=" * 80)
    report_lines.append("")

    # Dataset information
    total_train = sum(train_counts.values())
    total_test = sum(test_counts.values())

    report_lines.append("📊 INFORMASI DATASET:")
    report_lines.append(f"- Total gambar training: {total_train}")
    report_lines.append(f"- Total gambar testing: {total_test}")
    report_lines.append(f"- Total dataset: {total_train + total_test}")
    report_lines.append(f"- Jumlah kelas: {len(class_names)}")
    report_lines.append(f"- Ukuran gambar: 224x224 pixels")
    report_lines.append("")

    # Class distribution
    report_lines.append("📋 DISTRIBUSI KELAS:")
    for class_name in class_names:
        train_count = train_counts.get(class_name, 0)
        test_count = test_counts.get(class_name, 0)
        total_count = train_count + test_count
        percentage = (total_count / (total_train + total_test)) * 100
        report_lines.append(f"- {class_name}: {total_count} gambar ({percentage:.1f}%)")
    report_lines.append("")

    # Model architecture
    report_lines.append("🏗️ ARSITEKTUR MODEL:")
    report_lines.append(f"- Total parameters: {model.count_params():,}")
    report_lines.append(f"- Model size: {evaluation_results['model_size_mb']:.2f} MB")
    report_lines.append(f"- Architecture: Custom CNN dengan paired convolutions")
    report_lines.append(f"- Optimizer: Adam (lr=0.001)")
    report_lines.append(f"- Loss function: Categorical crossentropy")
    report_lines.append("")

    # Training results
    report_lines.append("🏋️ HASIL TRAINING:")
    report_lines.append(f"- Best validation accuracy: {training_summary['best_val_acc']:.4f}")
    report_lines.append(f"- Final training accuracy: {training_summary['final_train_acc']:.4f}")
    report_lines.append(f"- Final validation accuracy: {training_summary['final_val_acc']:.4f}")
    report_lines.append(f"- Overfitting gap: {training_summary['overfitting_gap']:.4f}")
    report_lines.append(f"- Best epoch: {training_summary['best_epoch']}")
    report_lines.append("")

    # Test results
    report_lines.append("🎯 HASIL EVALUASI:")
    report_lines.append(f"- Overall accuracy: {evaluation_results['accuracy']:.4f}")
    report_lines.append(f"- Macro-average precision: {evaluation_results['precision_macro']:.4f}")
    report_lines.append(f"- Macro-average recall: {evaluation_results['recall_macro']:.4f}")
    report_lines.append(f"- Macro-average F1-score: {evaluation_results['f1_macro']:.4f}")
    report_lines.append(f"- Macro-average AUC: {roc_results['macro']:.4f}")
    report_lines.append(f"- Micro-average AUC: {roc_results['micro']:.4f}")
    report_lines.append("")

    # Per-class results
    report_lines.append("📈 HASIL PER KELAS:")
    for i, class_name in enumerate(class_names):
        class_acc = evaluation_results['class_accuracies'][i]
        class_auc = roc_results[i]
        report_lines.append(f"- {class_name}:")
        report_lines.append(f"  Accuracy: {class_acc:.4f}")
        report_lines.append(f"  AUC: {class_auc:.4f}")
    report_lines.append("")

    # Statistical analysis
    report_lines.append("📊 ANALISIS STATISTIK:")
    class_accs = evaluation_results['class_accuracies']
    acc_mean = np.mean(class_accs)
    acc_std = np.std(class_accs)
    acc_cv = (acc_std / acc_mean) * 100 if acc_mean != 0 else 0

    report_lines.append(f"- Mean class accuracy: {acc_mean:.4f} ± {acc_std:.4f}")
    report_lines.append(f"- Coefficient of variation: {acc_cv:.2f}%")

    if acc_cv < 10:
        stability = "Sangat stabil"
    elif acc_cv < 20:
        stability = "Stabil"
    else:
        stability = "Kurang stabil"

    report_lines.append(f"- Model stability: {stability}")
    report_lines.append("")

    # Recommendations
    report_lines.append("💡 REKOMENDASI UNTUK SKRIPSI:")
    report_lines.append("1. Sertakan semua visualisasi dalam bab Results")
    report_lines.append("2. Diskusikan interpretasi hasil di bab Discussion")
    report_lines.append("3. Bandingkan dengan penelitian serupa")
    report_lines.append("4. Jelaskan trade-off complexity vs performance")
    report_lines.append("5. Diskusikan class imbalance jika ada")
    report_lines.append("")

    # Write to file
    with open('bird_classification_thesis_report.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))

    # Print to console
    print('\n'.join(report_lines))

    print("💾 Laporan thesis disimpan ke: bird_classification_thesis_report.txt")

    return report_lines

# Jalankan final report generation
final_report = generate_final_thesis_report(
    evaluation_results, roc_results, training_summary,
    train_counts, test_counts,
    ['Lonchura leucogastroides', 'Lonchura maja', 'Lonchura punctulata', 'Passer montanus', 'Unknown']
)

# ============================================================================
# CELL 10: SUMMARY DAN CHECKLIST UNTUK SKRIPSI
# ============================================================================

print("\n🎉 EVALUASI LENGKAP SELESAI!")
print("=" * 50)

print("\n✅ KOMPONEN YANG TELAH DIBUAT UNTUK SKRIPSI:")
print("1. ✅ Exploratory Data Analysis (EDA)")
print("2. ✅ Sample Images Visualization")
print("3. ✅ Enhanced Training History")
print("4. ✅ Comprehensive Model Evaluation")
print("5. ✅ Enhanced Confusion Matrix")
print("6. ✅ ROC Curve Analysis")
print("7. ✅ Performance Summary Table")
print("8. ✅ Final Thesis Report")

print("\n📁 FILE YANG DIHASILKAN:")
print("- dataset_eda_analysis.png")
print("- sample_images_per_class.png")
print("- enhanced_training_history.png")
print("- enhanced_confusion_matrix.png")
print("- comprehensive_roc_analysis.png")
print("- comprehensive_performance_summary.png")
print("- bird_classification_performance_summary.csv")
print("- bird_classification_thesis_report.txt")

print("\n📝 UNTUK SKRIPSI:")
print("✅ Semua komponen evaluasi yang diperlukan sudah lengkap")
print("✅ Visualisasi siap untuk dimasukkan ke dalam thesis")
print("✅ Metrik evaluasi sesuai standar akademik")
print("✅ Dokumentasi lengkap untuk analisis dan diskusi")

print("\n🎯 LANGKAH SELANJUTNYA:")
print("1. Copy-paste kode ini ke notebook Anda sesuai urutan cell")
print("2. Sesuaikan path dataset dengan struktur folder Anda")
print("3. Sesuaikan nama kelas dengan dataset Anda")
print("4. Jalankan setiap cell secara berurutan")
print("5. Gunakan hasil visualisasi untuk thesis Anda")

# ============================================================================
# CELL 11: KONFIGURASI UNTUK COPY-PASTE (SESUAIKAN INI)
# ============================================================================

# SESUAIKAN KONFIGURASI INI DENGAN SETUP ANDA:
"""
KONFIGURASI YANG PERLU DISESUAIKAN:

1. PATH DATASET:
   - train_dir = '/content/dataset/train'  # Ganti dengan path training Anda
   - test_dir = '/content/dataset/test'    # Ganti dengan path testing Anda

2. NAMA KELAS:
   - class_names = ['Lonchura leucogastroides', 'Lonchura maja', 'Lonchura punctulata', 'Passer montanus', 'Unknown']
   # Ganti dengan nama kelas burung Anda

3. PATH MODEL:
   - model_path = './models/best_bird_cnn_model.h5'  # Ganti dengan path model Anda

4. TARGET SIZE:
   - target_size = (224, 224)  # Sesuaikan dengan input size model Anda

5. BATCH SIZE:
   - batch_size = 16  # Sesuaikan dengan memory GPU Anda
"""

print("\n🔧 PETUNJUK KONFIGURASI:")
print("Sebelum menjalankan kode, pastikan untuk:")
print("1. Sesuaikan semua path dataset dengan struktur folder Anda")
print("2. Sesuaikan nama kelas dengan dataset Anda")
print("3. Sesuaikan path model dengan lokasi model yang sudah ditraining")
print("4. Pastikan semua library sudah terinstall")
print("5. Jalankan cell secara berurutan dari atas ke bawah")
