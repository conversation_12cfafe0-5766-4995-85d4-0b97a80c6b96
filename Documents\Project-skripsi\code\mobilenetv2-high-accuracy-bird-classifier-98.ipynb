{"metadata": {"kernelspec": {"language": "python", "display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.13", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kaggle": {"accelerator": "gpu", "dataSources": [{"sourceId": 8675724, "sourceType": "datasetVersion", "datasetId": 5200219}, {"sourceId": 8680931, "sourceType": "datasetVersion", "datasetId": 5204083}], "dockerImageVersionId": 30732, "isInternetEnabled": true, "language": "python", "sourceType": "notebook", "isGpuEnabled": true}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "code", "source": "import tensorflow as tf\nfrom tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense\nfrom tensorflow.keras.optimizers import Adam\nfrom tensorflow.keras.applications import MobileNetV2\nfrom tensorflow.keras.models import Model\nfrom tensorflow.keras.preprocessing.image import ImageDataGenerator\nfrom sklearn.utils.class_weight import compute_class_weight\nimport numpy as np\nimport matplotlib.pyplot as plt\nimport seaborn as sns\nfrom sklearn.metrics import confusion_matrix\n", "metadata": {"_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "execution": {"iopub.status.busy": "2024-06-13T09:01:34.170710Z", "iopub.execute_input": "2024-06-13T09:01:34.171117Z", "iopub.status.idle": "2024-06-13T09:01:46.493077Z", "shell.execute_reply.started": "2024-06-13T09:01:34.171085Z", "shell.execute_reply": "2024-06-13T09:01:46.492271Z"}, "trusted": true}, "execution_count": 1, "outputs": [{"name": "stderr", "text": "2024-06-13 09:01:35.769079: E external/local_xla/xla/stream_executor/cuda/cuda_dnn.cc:9261] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n2024-06-13 09:01:35.769201: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:607] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n2024-06-13 09:01:35.891484: E external/local_xla/xla/stream_executor/cuda/cuda_blas.cc:1515] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "output_type": "stream"}]}, {"cell_type": "markdown", "source": "# Define the input shape and number of classes", "metadata": {}}, {"cell_type": "code", "source": "input_shape = (224, 224, 3)\nnum_classes = 15", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:01:46.495191Z", "iopub.execute_input": "2024-06-13T09:01:46.495858Z", "iopub.status.idle": "2024-06-13T09:01:46.500275Z", "shell.execute_reply.started": "2024-06-13T09:01:46.495822Z", "shell.execute_reply": "2024-06-13T09:01:46.499443Z"}, "trusted": true}, "execution_count": 2, "outputs": []}, {"cell_type": "markdown", "source": "# Define data generators with additional data augmentation", "metadata": {}}, {"cell_type": "code", "source": "train_datagen = ImageDataGenerator(\n    rescale=1./255,\n    rotation_range=20,\n    width_shift_range=0.2,\n    height_shift_range=0.2,\n    shear_range=0.2,\n    zoom_range=0.2,\n    horizontal_flip=True,\n    vertical_flip=True,\n    fill_mode='nearest'\n)\n\ntest_datagen = ImageDataGenerator(rescale=1./255)\n", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:01:46.501302Z", "iopub.execute_input": "2024-06-13T09:01:46.501550Z", "iopub.status.idle": "2024-06-13T09:01:46.520550Z", "shell.execute_reply.started": "2024-06-13T09:01:46.501529Z", "shell.execute_reply": "2024-06-13T09:01:46.519539Z"}, "trusted": true}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": "# Set batch size\nbatch_size = 32\n\n# Define train and test generators\ntrain_generator = train_datagen.flow_from_directory(\n    '/kaggle/input/birds-15/BIRDS_V2/Train',\n    target_size=(224, 224),\n    batch_size=batch_size,\n    class_mode='categorical',\n    shuffle=True\n)\n\ntest_generator = test_datagen.flow_from_directory(\n    '/kaggle/input/birds-15/BIRDS_V2/test',\n    target_size=(224, 224),\n    batch_size=batch_size,\n    class_mode='categorical',\n    shuffle=False\n)\n", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:01:46.521827Z", "iopub.execute_input": "2024-06-13T09:01:46.522220Z", "iopub.status.idle": "2024-06-13T09:02:07.446026Z", "shell.execute_reply.started": "2024-06-13T09:01:46.522183Z", "shell.execute_reply": "2024-06-13T09:02:07.445174Z"}, "trusted": true}, "execution_count": 4, "outputs": [{"name": "stdout", "text": "Found 23702 images belonging to 15 classes.\nFound 6402 images belonging to 15 classes.\n", "output_type": "stream"}]}, {"cell_type": "markdown", "source": "# Load MobileNetV2 as base model", "metadata": {}}, {"cell_type": "code", "source": "base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=input_shape)", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:02:07.448568Z", "iopub.execute_input": "2024-06-13T09:02:07.448888Z", "iopub.status.idle": "2024-06-13T09:02:10.377451Z", "shell.execute_reply.started": "2024-06-13T09:02:07.448864Z", "shell.execute_reply": "2024-06-13T09:02:10.376419Z"}, "trusted": true}, "execution_count": 5, "outputs": [{"name": "stdout", "text": "Downloading data from https://storage.googleapis.com/tensorflow/keras-applications/mobilenet_v2/mobilenet_v2_weights_tf_dim_ordering_tf_kernels_1.0_224_no_top.h5\n\u001b[1m9406464/9406464\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 0us/step\n", "output_type": "stream"}]}, {"cell_type": "markdown", "source": "# Freeze layers in base model", "metadata": {}}, {"cell_type": "code", "source": "for layer in base_model.layers:\n    layer.trainable = False", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:02:10.378843Z", "iopub.execute_input": "2024-06-13T09:02:10.379133Z", "iopub.status.idle": "2024-06-13T09:02:10.387231Z", "shell.execute_reply.started": "2024-06-13T09:02:10.379109Z", "shell.execute_reply": "2024-06-13T09:02:10.386225Z"}, "trusted": true}, "execution_count": 6, "outputs": []}, {"cell_type": "markdown", "source": "# Add custom head", "metadata": {}}, {"cell_type": "code", "source": "x = base_model.output\nx = GlobalAveragePooling2D()(x)\nx = Dropout(0.5)(x)\nx = Dense(1024, activation='relu')(x)\nx = Dropout(0.5)(x)\npredictions = Dense(num_classes, activation='softmax')(x)", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:02:10.388402Z", "iopub.execute_input": "2024-06-13T09:02:10.388703Z", "iopub.status.idle": "2024-06-13T09:02:10.423174Z", "shell.execute_reply.started": "2024-06-13T09:02:10.388678Z", "shell.execute_reply": "2024-06-13T09:02:10.422290Z"}, "trusted": true}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": "# Combine base model and custom head\nmodel = Model(inputs=base_model.input, outputs=predictions)\n\n# Fine-tune the model by unfreezing some layers\nfor layer in base_model.layers[:-10]:\n    layer.trainable = True\n\n# Implement learning rate scheduling\nlr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(\n    initial_learning_rate=1e-4,\n    decay_steps=1000,\n    decay_rate=0.9\n)\noptimizer = Adam(learning_rate=lr_schedule)\n", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:02:10.424276Z", "iopub.execute_input": "2024-06-13T09:02:10.424548Z", "iopub.status.idle": "2024-06-13T09:02:10.461203Z", "shell.execute_reply.started": "2024-06-13T09:02:10.424526Z", "shell.execute_reply": "2024-06-13T09:02:10.460412Z"}, "trusted": true}, "execution_count": 8, "outputs": []}, {"cell_type": "markdown", "source": "# Compile the model with class weights", "metadata": {}}, {"cell_type": "code", "source": "model.compile(optimizer=optimizer, loss='categorical_crossentropy', metrics=['accuracy'])\n", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:02:10.462309Z", "iopub.execute_input": "2024-06-13T09:02:10.462624Z", "iopub.status.idle": "2024-06-13T09:02:10.476007Z", "shell.execute_reply.started": "2024-06-13T09:02:10.462601Z", "shell.execute_reply": "2024-06-13T09:02:10.475191Z"}, "trusted": true}, "execution_count": 9, "outputs": []}, {"cell_type": "markdown", "source": "# Print model summary", "metadata": {}}, {"cell_type": "code", "source": "model.summary()\n", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:02:10.477130Z", "iopub.execute_input": "2024-06-13T09:02:10.477464Z", "iopub.status.idle": "2024-06-13T09:02:10.697841Z", "shell.execute_reply.started": "2024-06-13T09:02:10.477434Z", "shell.execute_reply": "2024-06-13T09:02:10.696902Z"}, "trusted": true}, "execution_count": 10, "outputs": [{"output_type": "display_data", "data": {"text/plain": "\u001b[1mModel: \"functional_1\"\u001b[0m\n", "text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"functional_1\"</span>\n</pre>\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "┏━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━┓\n┃\u001b[1m \u001b[0m\u001b[1mLayer (type)       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape     \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m   Param #\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mConnected to     \u001b[0m\u001b[1m \u001b[0m┃\n┡━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━┩\n│ input_layer         │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m,  │          \u001b[38;5;34m0\u001b[0m │ -                 │\n│ (\u001b[38;5;33mInputLayer\u001b[0m)        │ \u001b[38;5;34m3\u001b[0m)                │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ Conv1 (\u001b[38;5;33mConv2D\u001b[0m)      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │        \u001b[38;5;34m864\u001b[0m │ input_layer[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m] │\n│                     │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ bn_Conv1            │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │        \u001b[38;5;34m128\u001b[0m │ Conv1[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]       │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ Conv1_relu (\u001b[38;5;33mReLU\u001b[0m)   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │          \u001b[38;5;34m0\u001b[0m │ bn_Conv1[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]    │\n│                     │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ expanded_conv_dept… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │        \u001b[38;5;34m288\u001b[0m │ Conv1_relu[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]  │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ expanded_conv_dept… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │        \u001b[38;5;34m128\u001b[0m │ expanded_conv_de… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ expanded_conv_dept… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │          \u001b[38;5;34m0\u001b[0m │ expanded_conv_de… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ expanded_conv_proj… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │        \u001b[38;5;34m512\u001b[0m │ expanded_conv_de… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m16\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ expanded_conv_proj… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │         \u001b[38;5;34m64\u001b[0m │ expanded_conv_pr… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m16\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_expand      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │      \u001b[38;5;34m1,536\u001b[0m │ expanded_conv_pr… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_expand_BN   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │        \u001b[38;5;34m384\u001b[0m │ block_1_expand[\u001b[38;5;34m0\u001b[0m… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_expand_relu │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m,  │          \u001b[38;5;34m0\u001b[0m │ block_1_expand_B… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_pad         │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m113\u001b[0m, \u001b[38;5;34m113\u001b[0m,  │          \u001b[38;5;34m0\u001b[0m │ block_1_expand_r… │\n│ (\u001b[38;5;33mZeroPadding2D\u001b[0m)     │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_depthwise   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │        \u001b[38;5;34m864\u001b[0m │ block_1_pad[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m] │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │        \u001b[38;5;34m384\u001b[0m │ block_1_depthwis… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_1_depthwis… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_project     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │      \u001b[38;5;34m2,304\u001b[0m │ block_1_depthwis… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m24\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_project_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │         \u001b[38;5;34m96\u001b[0m │ block_1_project[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m24\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_expand      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │      \u001b[38;5;34m3,456\u001b[0m │ block_1_project_… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_expand_BN   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │        \u001b[38;5;34m576\u001b[0m │ block_2_expand[\u001b[38;5;34m0\u001b[0m… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_expand_relu │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_2_expand_B… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_depthwise   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │      \u001b[38;5;34m1,296\u001b[0m │ block_2_expand_r… │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │        \u001b[38;5;34m576\u001b[0m │ block_2_depthwis… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_2_depthwis… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_project     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │      \u001b[38;5;34m3,456\u001b[0m │ block_2_depthwis… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m24\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_project_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │         \u001b[38;5;34m96\u001b[0m │ block_2_project[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m24\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_add (\u001b[38;5;33mAdd\u001b[0m)   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_1_project_… │\n│                     │ \u001b[38;5;34m24\u001b[0m)               │            │ block_2_project_… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_expand      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │      \u001b[38;5;34m3,456\u001b[0m │ block_2_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m] │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_expand_BN   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │        \u001b[38;5;34m576\u001b[0m │ block_3_expand[\u001b[38;5;34m0\u001b[0m… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_expand_relu │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_3_expand_B… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_pad         │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m57\u001b[0m, \u001b[38;5;34m57\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_3_expand_r… │\n│ (\u001b[38;5;33mZeroPadding2D\u001b[0m)     │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_depthwise   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │      \u001b[38;5;34m1,296\u001b[0m │ block_3_pad[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m] │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │        \u001b[38;5;34m576\u001b[0m │ block_3_depthwis… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_3_depthwis… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m144\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_project     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │      \u001b[38;5;34m4,608\u001b[0m │ block_3_depthwis… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_project_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │        \u001b[38;5;34m128\u001b[0m │ block_3_project[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_expand      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │      \u001b[38;5;34m6,144\u001b[0m │ block_3_project_… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_expand_BN   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │        \u001b[38;5;34m768\u001b[0m │ block_4_expand[\u001b[38;5;34m0\u001b[0m… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_expand_relu │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_4_expand_B… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_depthwise   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │      \u001b[38;5;34m1,728\u001b[0m │ block_4_expand_r… │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │        \u001b[38;5;34m768\u001b[0m │ block_4_depthwis… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_4_depthwis… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_project     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │      \u001b[38;5;34m6,144\u001b[0m │ block_4_depthwis… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_project_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │        \u001b[38;5;34m128\u001b[0m │ block_4_project[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_add (\u001b[38;5;33mAdd\u001b[0m)   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_3_project_… │\n│                     │ \u001b[38;5;34m32\u001b[0m)               │            │ block_4_project_… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_expand      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │      \u001b[38;5;34m6,144\u001b[0m │ block_4_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m] │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_expand_BN   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │        \u001b[38;5;34m768\u001b[0m │ block_5_expand[\u001b[38;5;34m0\u001b[0m… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_expand_relu │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_5_expand_B… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_depthwise   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │      \u001b[38;5;34m1,728\u001b[0m │ block_5_expand_r… │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │        \u001b[38;5;34m768\u001b[0m │ block_5_depthwis… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_5_depthwis… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_project     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │      \u001b[38;5;34m6,144\u001b[0m │ block_5_depthwis… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_project_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │        \u001b[38;5;34m128\u001b[0m │ block_5_project[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m32\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_add (\u001b[38;5;33mAdd\u001b[0m)   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_4_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m… │\n│                     │ \u001b[38;5;34m32\u001b[0m)               │            │ block_5_project_… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_expand      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │      \u001b[38;5;34m6,144\u001b[0m │ block_5_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m] │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_expand_BN   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │        \u001b[38;5;34m768\u001b[0m │ block_6_expand[\u001b[38;5;34m0\u001b[0m… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_expand_relu │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_6_expand_B… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_pad         │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m29\u001b[0m, \u001b[38;5;34m29\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_6_expand_r… │\n│ (\u001b[38;5;33mZeroPadding2D\u001b[0m)     │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_depthwise   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m1,728\u001b[0m │ block_6_pad[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m] │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │        \u001b[38;5;34m768\u001b[0m │ block_6_depthwis… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_6_depthwis… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m192\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_project     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m12,288\u001b[0m │ block_6_depthwis… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m64\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_project_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │        \u001b[38;5;34m256\u001b[0m │ block_6_project[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m64\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_expand      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m24,576\u001b[0m │ block_6_project_… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_expand_BN   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m1,536\u001b[0m │ block_7_expand[\u001b[38;5;34m0\u001b[0m… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_expand_relu │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_7_expand_B… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_depthwise   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m3,456\u001b[0m │ block_7_expand_r… │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m1,536\u001b[0m │ block_7_depthwis… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_7_depthwis… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_project     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m24,576\u001b[0m │ block_7_depthwis… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m64\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_project_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │        \u001b[38;5;34m256\u001b[0m │ block_7_project[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m64\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_add (\u001b[38;5;33mAdd\u001b[0m)   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_6_project_… │\n│                     │ \u001b[38;5;34m64\u001b[0m)               │            │ block_7_project_… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_expand      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m24,576\u001b[0m │ block_7_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m] │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_expand_BN   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m1,536\u001b[0m │ block_8_expand[\u001b[38;5;34m0\u001b[0m… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_expand_relu │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_8_expand_B… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_depthwise   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m3,456\u001b[0m │ block_8_expand_r… │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m1,536\u001b[0m │ block_8_depthwis… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_8_depthwis… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_project     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m24,576\u001b[0m │ block_8_depthwis… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m64\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_project_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │        \u001b[38;5;34m256\u001b[0m │ block_8_project[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m64\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_add (\u001b[38;5;33mAdd\u001b[0m)   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_7_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m… │\n│                     │ \u001b[38;5;34m64\u001b[0m)               │            │ block_8_project_… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_expand      │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m24,576\u001b[0m │ block_8_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m] │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_expand_BN   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m1,536\u001b[0m │ block_9_expand[\u001b[38;5;34m0\u001b[0m… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_expand_relu │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_9_expand_B… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_depthwise   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m3,456\u001b[0m │ block_9_expand_r… │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m1,536\u001b[0m │ block_9_depthwis… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_depthwise_… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_9_depthwis… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_project     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m24,576\u001b[0m │ block_9_depthwis… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m64\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_project_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │        \u001b[38;5;34m256\u001b[0m │ block_9_project[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m64\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_add (\u001b[38;5;33mAdd\u001b[0m)   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_8_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m… │\n│                     │ \u001b[38;5;34m64\u001b[0m)               │            │ block_9_project_… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_expand     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m24,576\u001b[0m │ block_9_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m] │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_expand_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m1,536\u001b[0m │ block_10_expand[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_expand_re… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_10_expand_… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_depthwise  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m3,456\u001b[0m │ block_10_expand_… │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m1,536\u001b[0m │ block_10_depthwi… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_10_depthwi… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m384\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_project    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m36,864\u001b[0m │ block_10_depthwi… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_project_BN │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │        \u001b[38;5;34m384\u001b[0m │ block_10_project… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_expand     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m55,296\u001b[0m │ block_10_project… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_expand_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m2,304\u001b[0m │ block_11_expand[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_expand_re… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_11_expand_… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_depthwise  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m5,184\u001b[0m │ block_11_expand_… │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m2,304\u001b[0m │ block_11_depthwi… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_11_depthwi… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_project    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m55,296\u001b[0m │ block_11_depthwi… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_project_BN │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │        \u001b[38;5;34m384\u001b[0m │ block_11_project… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_add (\u001b[38;5;33mAdd\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_10_project… │\n│                     │ \u001b[38;5;34m96\u001b[0m)               │            │ block_11_project… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_expand     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m55,296\u001b[0m │ block_11_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_expand_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m2,304\u001b[0m │ block_12_expand[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_expand_re… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_12_expand_… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_depthwise  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m5,184\u001b[0m │ block_12_expand_… │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m2,304\u001b[0m │ block_12_depthwi… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_12_depthwi… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_project    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m55,296\u001b[0m │ block_12_depthwi… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_project_BN │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │        \u001b[38;5;34m384\u001b[0m │ block_12_project… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m96\u001b[0m)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_add (\u001b[38;5;33mAdd\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_11_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m…\u001b[0m │\n│                     │ \u001b[38;5;34m96\u001b[0m)               │            │ block_12_project… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_expand     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │     \u001b[38;5;34m55,296\u001b[0m │ block_12_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_expand_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │      \u001b[38;5;34m2,304\u001b[0m │ block_13_expand[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_expand_re… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_13_expand_… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_pad        │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m15\u001b[0m, \u001b[38;5;34m15\u001b[0m,    │          \u001b[38;5;34m0\u001b[0m │ block_13_expand_… │\n│ (\u001b[38;5;33mZeroPadding2D\u001b[0m)     │ \u001b[38;5;34m576\u001b[0m)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_depthwise  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m576\u001b[0m) │      \u001b[38;5;34m5,184\u001b[0m │ block_13_pad[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m576\u001b[0m) │      \u001b[38;5;34m2,304\u001b[0m │ block_13_depthwi… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m576\u001b[0m) │          \u001b[38;5;34m0\u001b[0m │ block_13_depthwi… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_project    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m160\u001b[0m) │     \u001b[38;5;34m92,160\u001b[0m │ block_13_depthwi… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_project_BN │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m160\u001b[0m) │        \u001b[38;5;34m640\u001b[0m │ block_13_project… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_expand     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │    \u001b[38;5;34m153,600\u001b[0m │ block_13_project… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_expand_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │      \u001b[38;5;34m3,840\u001b[0m │ block_14_expand[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_expand_re… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │          \u001b[38;5;34m0\u001b[0m │ block_14_expand_… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_depthwise  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │      \u001b[38;5;34m8,640\u001b[0m │ block_14_expand_… │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │      \u001b[38;5;34m3,840\u001b[0m │ block_14_depthwi… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │          \u001b[38;5;34m0\u001b[0m │ block_14_depthwi… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_project    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m160\u001b[0m) │    \u001b[38;5;34m153,600\u001b[0m │ block_14_depthwi… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_project_BN │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m160\u001b[0m) │        \u001b[38;5;34m640\u001b[0m │ block_14_project… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_add (\u001b[38;5;33mAdd\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m160\u001b[0m) │          \u001b[38;5;34m0\u001b[0m │ block_13_project… │\n│                     │                   │            │ block_14_project… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_expand     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │    \u001b[38;5;34m153,600\u001b[0m │ block_14_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_expand_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │      \u001b[38;5;34m3,840\u001b[0m │ block_15_expand[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_expand_re… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │          \u001b[38;5;34m0\u001b[0m │ block_15_expand_… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_depthwise  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │      \u001b[38;5;34m8,640\u001b[0m │ block_15_expand_… │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │      \u001b[38;5;34m3,840\u001b[0m │ block_15_depthwi… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │          \u001b[38;5;34m0\u001b[0m │ block_15_depthwi… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_project    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m160\u001b[0m) │    \u001b[38;5;34m153,600\u001b[0m │ block_15_depthwi… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_project_BN │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m160\u001b[0m) │        \u001b[38;5;34m640\u001b[0m │ block_15_project… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_add (\u001b[38;5;33mAdd\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m160\u001b[0m) │          \u001b[38;5;34m0\u001b[0m │ block_14_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m…\u001b[0m │\n│                     │                   │            │ block_15_project… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_expand     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │    \u001b[38;5;34m153,600\u001b[0m │ block_15_add[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_expand_BN  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │      \u001b[38;5;34m3,840\u001b[0m │ block_16_expand[\u001b[38;5;34m…\u001b[0m │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_expand_re… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │          \u001b[38;5;34m0\u001b[0m │ block_16_expand_… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_depthwise  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │      \u001b[38;5;34m8,640\u001b[0m │ block_16_expand_… │\n│ (\u001b[38;5;33mDepthwiseConv2D\u001b[0m)   │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │      \u001b[38;5;34m3,840\u001b[0m │ block_16_depthwi… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_depthwise… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m960\u001b[0m) │          \u001b[38;5;34m0\u001b[0m │ block_16_depthwi… │\n│ (\u001b[38;5;33mReLU\u001b[0m)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_project    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m320\u001b[0m) │    \u001b[38;5;34m307,200\u001b[0m │ block_16_depthwi… │\n│ (\u001b[38;5;33mConv2D\u001b[0m)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_project_BN │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m320\u001b[0m) │      \u001b[38;5;34m1,280\u001b[0m │ block_16_project… │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ Conv_1 (\u001b[38;5;33mConv2D\u001b[0m)     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m,      │    \u001b[38;5;34m409,600\u001b[0m │ block_16_project… │\n│                     │ \u001b[38;5;34m1280\u001b[0m)             │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ Conv_1_bn           │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m,      │      \u001b[38;5;34m5,120\u001b[0m │ Conv_1[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]      │\n│ (\u001b[38;5;33mBatchNormalizatio…\u001b[0m │ \u001b[38;5;34m1280\u001b[0m)             │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ out_relu (\u001b[38;5;33mReLU\u001b[0m)     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m7\u001b[0m, \u001b[38;5;34m7\u001b[0m,      │          \u001b[38;5;34m0\u001b[0m │ Conv_1_bn[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]   │\n│                     │ \u001b[38;5;34m1280\u001b[0m)             │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ global_average_poo… │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m1280\u001b[0m)      │          \u001b[38;5;34m0\u001b[0m │ out_relu[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]    │\n│ (\u001b[38;5;33mGlobalAveragePool…\u001b[0m │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ dropout (\u001b[38;5;33mDropout\u001b[0m)   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m1280\u001b[0m)      │          \u001b[38;5;34m0\u001b[0m │ global_average_p… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ dense (\u001b[38;5;33mDense\u001b[0m)       │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m1024\u001b[0m)      │  \u001b[38;5;34m1,311,744\u001b[0m │ dropout[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]     │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ dropout_1 (\u001b[38;5;33mDropout\u001b[0m) │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m1024\u001b[0m)      │          \u001b[38;5;34m0\u001b[0m │ dense[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]       │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ dense_1 (\u001b[38;5;33mDense\u001b[0m)     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m15\u001b[0m)        │     \u001b[38;5;34m15,375\u001b[0m │ dropout_1[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]   │\n└─────────────────────┴───────────────────┴────────────┴───────────────────┘\n", "text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━┓\n┃<span style=\"font-weight: bold\"> Layer (type)        </span>┃<span style=\"font-weight: bold\"> Output Shape      </span>┃<span style=\"font-weight: bold\">    Param # </span>┃<span style=\"font-weight: bold\"> Connected to      </span>┃\n┡━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━┩\n│ input_layer         │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>,  │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                 │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)        │ <span style=\"color: #00af00; text-decoration-color: #00af00\">3</span>)                │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ Conv1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │        <span style=\"color: #00af00; text-decoration-color: #00af00\">864</span> │ input_layer[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>] │\n│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ bn_Conv1            │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │        <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span> │ Conv1[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]       │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ Conv1_relu (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ bn_Conv1[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]    │\n│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ expanded_conv_dept… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │        <span style=\"color: #00af00; text-decoration-color: #00af00\">288</span> │ Conv1_relu[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]  │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ expanded_conv_dept… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │        <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span> │ expanded_conv_de… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ expanded_conv_dept… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ expanded_conv_de… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ expanded_conv_proj… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │        <span style=\"color: #00af00; text-decoration-color: #00af00\">512</span> │ expanded_conv_de… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">16</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ expanded_conv_proj… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │         <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span> │ expanded_conv_pr… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">16</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_expand      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,536</span> │ expanded_conv_pr… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_expand_BN   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │        <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span> │ block_1_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_expand_relu │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>,  │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_1_expand_B… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_pad         │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">113</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">113</span>,  │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_1_expand_r… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ZeroPadding2D</span>)     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_depthwise   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">864</span> │ block_1_pad[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>] │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span> │ block_1_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_1_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_project     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">2,304</span> │ block_1_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">24</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_1_project_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │         <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span> │ block_1_project[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">24</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_expand      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,456</span> │ block_1_project_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_expand_BN   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span> │ block_2_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_expand_relu │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_2_expand_B… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_depthwise   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,296</span> │ block_2_expand_r… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span> │ block_2_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_2_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_project     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,456</span> │ block_2_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">24</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_project_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │         <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span> │ block_2_project[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">24</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_2_add (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Add</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_1_project_… │\n│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">24</span>)               │            │ block_2_project_… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_expand      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,456</span> │ block_2_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>] │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_expand_BN   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span> │ block_3_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_expand_relu │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_3_expand_B… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_pad         │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">57</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">57</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_3_expand_r… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ZeroPadding2D</span>)     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_depthwise   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,296</span> │ block_3_pad[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>] │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span> │ block_3_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_3_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">144</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_project     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">4,608</span> │ block_3_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_3_project_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span> │ block_3_project[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_expand      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">6,144</span> │ block_3_project_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_expand_BN   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">768</span> │ block_4_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_expand_relu │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_4_expand_B… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_depthwise   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,728</span> │ block_4_expand_r… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">768</span> │ block_4_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_4_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_project     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">6,144</span> │ block_4_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_project_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span> │ block_4_project[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_4_add (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Add</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_3_project_… │\n│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │ block_4_project_… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_expand      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">6,144</span> │ block_4_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>] │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_expand_BN   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">768</span> │ block_5_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_expand_relu │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_5_expand_B… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_depthwise   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,728</span> │ block_5_expand_r… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">768</span> │ block_5_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_5_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_project     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">6,144</span> │ block_5_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_project_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span> │ block_5_project[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_5_add (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Add</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_4_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>… │\n│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)               │            │ block_5_project_… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_expand      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">6,144</span> │ block_5_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>] │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_expand_BN   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">768</span> │ block_6_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_expand_relu │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_6_expand_B… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_pad         │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">29</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">29</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_6_expand_r… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ZeroPadding2D</span>)     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_depthwise   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,728</span> │ block_6_pad[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>] │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">768</span> │ block_6_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_6_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">192</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_project     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">12,288</span> │ block_6_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_6_project_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">256</span> │ block_6_project[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_expand      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">24,576</span> │ block_6_project_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_expand_BN   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,536</span> │ block_7_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_expand_relu │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_7_expand_B… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_depthwise   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,456</span> │ block_7_expand_r… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,536</span> │ block_7_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_7_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_project     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">24,576</span> │ block_7_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_project_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">256</span> │ block_7_project[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_7_add (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Add</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_6_project_… │\n│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │ block_7_project_… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_expand      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">24,576</span> │ block_7_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>] │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_expand_BN   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,536</span> │ block_8_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_expand_relu │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_8_expand_B… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_depthwise   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,456</span> │ block_8_expand_r… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,536</span> │ block_8_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_8_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_project     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">24,576</span> │ block_8_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_project_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">256</span> │ block_8_project[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_8_add (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Add</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_7_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>… │\n│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │ block_8_project_… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_expand      │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">24,576</span> │ block_8_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>] │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_expand_BN   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,536</span> │ block_9_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_expand_relu │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_9_expand_B… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_depthwise   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,456</span> │ block_9_expand_r… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,536</span> │ block_9_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_depthwise_… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_9_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_project     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">24,576</span> │ block_9_depthwis… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_project_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">256</span> │ block_9_project[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_9_add (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Add</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_8_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>… │\n│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)               │            │ block_9_project_… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_expand     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">24,576</span> │ block_9_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>] │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_expand_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,536</span> │ block_10_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_expand_re… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_10_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_depthwise  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,456</span> │ block_10_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,536</span> │ block_10_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_10_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_project    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">36,864</span> │ block_10_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_10_project_BN │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span> │ block_10_project… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_expand     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">55,296</span> │ block_10_project… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_expand_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">2,304</span> │ block_11_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_expand_re… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_11_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_depthwise  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">5,184</span> │ block_11_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">2,304</span> │ block_11_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_11_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_project    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">55,296</span> │ block_11_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_project_BN │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span> │ block_11_project… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_11_add (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Add</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_10_project… │\n│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │ block_11_project… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_expand     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">55,296</span> │ block_11_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_expand_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">2,304</span> │ block_12_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_expand_re… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_12_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_depthwise  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">5,184</span> │ block_12_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">2,304</span> │ block_12_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_12_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_project    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">55,296</span> │ block_12_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_project_BN │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">384</span> │ block_12_project… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_12_add (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Add</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_11_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">96</span>)               │            │ block_12_project… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_expand     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │     <span style=\"color: #00af00; text-decoration-color: #00af00\">55,296</span> │ block_12_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_expand_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │      <span style=\"color: #00af00; text-decoration-color: #00af00\">2,304</span> │ block_13_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_expand_re… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_13_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_pad        │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">15</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">15</span>,    │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_13_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ZeroPadding2D</span>)     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>)              │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_depthwise  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>) │      <span style=\"color: #00af00; text-decoration-color: #00af00\">5,184</span> │ block_13_pad[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>) │      <span style=\"color: #00af00; text-decoration-color: #00af00\">2,304</span> │ block_13_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">576</span>) │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_13_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_project    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">160</span>) │     <span style=\"color: #00af00; text-decoration-color: #00af00\">92,160</span> │ block_13_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_13_project_BN │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">160</span>) │        <span style=\"color: #00af00; text-decoration-color: #00af00\">640</span> │ block_13_project… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_expand     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │    <span style=\"color: #00af00; text-decoration-color: #00af00\">153,600</span> │ block_13_project… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_expand_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,840</span> │ block_14_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_expand_re… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_14_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_depthwise  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │      <span style=\"color: #00af00; text-decoration-color: #00af00\">8,640</span> │ block_14_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,840</span> │ block_14_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_14_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_project    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">160</span>) │    <span style=\"color: #00af00; text-decoration-color: #00af00\">153,600</span> │ block_14_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_project_BN │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">160</span>) │        <span style=\"color: #00af00; text-decoration-color: #00af00\">640</span> │ block_14_project… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_14_add (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Add</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">160</span>) │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_13_project… │\n│                     │                   │            │ block_14_project… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_expand     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │    <span style=\"color: #00af00; text-decoration-color: #00af00\">153,600</span> │ block_14_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_expand_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,840</span> │ block_15_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_expand_re… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_15_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_depthwise  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │      <span style=\"color: #00af00; text-decoration-color: #00af00\">8,640</span> │ block_15_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,840</span> │ block_15_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_15_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_project    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">160</span>) │    <span style=\"color: #00af00; text-decoration-color: #00af00\">153,600</span> │ block_15_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_project_BN │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">160</span>) │        <span style=\"color: #00af00; text-decoration-color: #00af00\">640</span> │ block_15_project… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_15_add (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Add</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">160</span>) │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_14_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│                     │                   │            │ block_15_project… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_expand     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │    <span style=\"color: #00af00; text-decoration-color: #00af00\">153,600</span> │ block_15_add[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_expand_BN  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,840</span> │ block_16_expand[<span style=\"color: #00af00; text-decoration-color: #00af00\">…</span> │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_expand_re… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_16_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_depthwise  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │      <span style=\"color: #00af00; text-decoration-color: #00af00\">8,640</span> │ block_16_expand_… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">DepthwiseConv2D</span>)   │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │      <span style=\"color: #00af00; text-decoration-color: #00af00\">3,840</span> │ block_16_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_depthwise… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">960</span>) │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ block_16_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)              │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_project    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">320</span>) │    <span style=\"color: #00af00; text-decoration-color: #00af00\">307,200</span> │ block_16_depthwi… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)            │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ block_16_project_BN │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">320</span>) │      <span style=\"color: #00af00; text-decoration-color: #00af00\">1,280</span> │ block_16_project… │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ Conv_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>,      │    <span style=\"color: #00af00; text-decoration-color: #00af00\">409,600</span> │ block_16_project… │\n│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">1280</span>)             │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ Conv_1_bn           │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>,      │      <span style=\"color: #00af00; text-decoration-color: #00af00\">5,120</span> │ Conv_1[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]      │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalizatio…</span> │ <span style=\"color: #00af00; text-decoration-color: #00af00\">1280</span>)             │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ out_relu (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReLU</span>)     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">7</span>,      │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ Conv_1_bn[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]   │\n│                     │ <span style=\"color: #00af00; text-decoration-color: #00af00\">1280</span>)             │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ global_average_poo… │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1280</span>)      │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ out_relu[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]    │\n│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">GlobalAveragePool…</span> │                   │            │                   │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ dropout (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dropout</span>)   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1280</span>)      │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ global_average_p… │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ dense (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)       │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1024</span>)      │  <span style=\"color: #00af00; text-decoration-color: #00af00\">1,311,744</span> │ dropout[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]     │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ dropout_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dropout</span>) │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1024</span>)      │          <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ dense[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]       │\n├─────────────────────┼───────────────────┼────────────┼───────────────────┤\n│ dense_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">15</span>)        │     <span style=\"color: #00af00; text-decoration-color: #00af00\">15,375</span> │ dropout_1[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]   │\n└─────────────────────┴───────────────────┴────────────┴───────────────────┘\n</pre>\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "\u001b[1m Total params: \u001b[0m\u001b[38;5;34m3,585,103\u001b[0m (13.68 MB)\n", "text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">3,585,103</span> (13.68 MB)\n</pre>\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m2,818,511\u001b[0m (10.75 MB)\n", "text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">2,818,511</span> (10.75 MB)\n</pre>\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m766,592\u001b[0m (2.92 MB)\n", "text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">766,592</span> (2.92 MB)\n</pre>\n"}, "metadata": {}}]}, {"cell_type": "code", "source": "# Implement early stopping callback\nearly_stopping = tf.keras.callbacks.EarlyStopping(\n    monitor='val_loss',\n    patience=3,\n    restore_best_weights=True\n)\n\n# Train the model with modified settings\nhistory = model.fit(\n    train_generator,\n    epochs=15,  # Increase epochs for better training\n    validation_data=test_generator,\n    callbacks=[early_stopping],\n    verbose=1\n)", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:02:10.699044Z", "iopub.execute_input": "2024-06-13T09:02:10.699296Z", "iopub.status.idle": "2024-06-13T09:43:06.998612Z", "shell.execute_reply.started": "2024-06-13T09:02:10.699275Z", "shell.execute_reply": "2024-06-13T09:43:06.997810Z"}, "trusted": true}, "execution_count": 11, "outputs": [{"name": "stdout", "text": "Epoch 1/15\n", "output_type": "stream"}, {"name": "stderr", "text": "/opt/conda/lib/python3.10/site-packages/keras/src/trainers/data_adapters/py_dataset_adapter.py:121: UserWarning: Your `PyDataset` class should call `super().__init__(**kwargs)` in its constructor. `**kwargs` can include `workers`, `use_multiprocessing`, `max_queue_size`. Do not pass these arguments to `fit()`, as they will be ignored.\n  self._warn_if_super_not_called()\n", "output_type": "stream"}, {"name": "stdout", "text": "\u001b[1m  1/741\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m12:19:47\u001b[0m 60s/step - accuracy: 0.0938 - loss: 3.9026", "output_type": "stream"}, {"name": "stderr", "text": "WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\nI0000 00:00:1718269391.641694     118 device_compiler.h:186] Compiled cluster using XLA!  This line is logged at most once for the lifetime of the process.\n", "output_type": "stream"}, {"name": "stdout", "text": "\u001b[1m 17/741\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m6:15\u001b[0m 519ms/step - accuracy: 0.0978 - loss: 3.4475", "output_type": "stream"}, {"name": "stderr", "text": "/opt/conda/lib/python3.10/site-packages/PIL/Image.py:992: UserWarning: Palette images with Transparency expressed in bytes should be converted to RGBA images\n  warnings.warn(\n", "output_type": "stream"}, {"name": "stdout", "text": "\u001b[1m741/741\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m554s\u001b[0m 668ms/step - accuracy: 0.5475 - loss: 1.5137 - val_accuracy: 0.9067 - val_loss: 0.3253\nEpoch 2/15\n\u001b[1m741/741\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m317s\u001b[0m 423ms/step - accuracy: 0.9284 - loss: 0.2311 - val_accuracy: 0.9364 - val_loss: 0.2342\nEpoch 3/15\n\u001b[1m741/741\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m318s\u001b[0m 425ms/step - accuracy: 0.9613 - loss: 0.1241 - val_accuracy: 0.9592 - val_loss: 0.1696\nEpoch 4/15\n\u001b[1m741/741\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m317s\u001b[0m 424ms/step - accuracy: 0.9679 - loss: 0.1004 - val_accuracy: 0.9677 - val_loss: 0.1597\nEpoch 5/15\n\u001b[1m741/741\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m315s\u001b[0m 422ms/step - accuracy: 0.9763 - loss: 0.0762 - val_accuracy: 0.9617 - val_loss: 0.1840\nEpoch 6/15\n\u001b[1m741/741\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m315s\u001b[0m 422ms/step - accuracy: 0.9806 - loss: 0.0619 - val_accuracy: 0.9655 - val_loss: 0.1821\nEpoch 7/15\n\u001b[1m741/741\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m320s\u001b[0m 428ms/step - accuracy: 0.9839 - loss: 0.0524 - val_accuracy: 0.9691 - val_loss: 0.1670\n", "output_type": "stream"}]}, {"cell_type": "code", "source": "", "metadata": {}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": "# Evaluate the model on test data", "metadata": {}}, {"cell_type": "code", "source": "test_loss, test_accuracy = model.evaluate(test_generator)\nprint(\"Test Loss:\", test_loss)\nprint(\"Test Accuracy:\", test_accuracy)", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:43:06.999726Z", "iopub.execute_input": "2024-06-13T09:43:07.000037Z", "iopub.status.idle": "2024-06-13T09:43:35.067231Z", "shell.execute_reply.started": "2024-06-13T09:43:06.999995Z", "shell.execute_reply": "2024-06-13T09:43:35.066421Z"}, "trusted": true}, "execution_count": 12, "outputs": [{"name": "stdout", "text": "\u001b[1m201/201\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m28s\u001b[0m 139ms/step - accuracy: 0.9420 - loss: 0.3315\nTest Loss: 0.1596904844045639\nTest Accuracy: 0.9676663279533386\n", "output_type": "stream"}]}, {"cell_type": "code", "source": "import numpy as np\nimport matplotlib.pyplot as plt\nimport seaborn as sns\nfrom tensorflow.keras.preprocessing.image import ImageDataGenerator\nfrom sklearn.metrics import confusion_matrix\n# Define the path to the validation directory\nvalidation_dir = '/kaggle/input/valid-birds/validation'\n\n# Define the data generators\nvalidation_datagen = ImageDataGenerator(rescale=1./255)\n\n# Load the validation data using the ImageDataGenerator\nvalidation_generator = validation_datagen.flow_from_directory(\n    validation_dir,\n    target_size=(224, 224),\n    batch_size=batch_size,\n    class_mode='categorical',\n    shuffle=False\n)\n\n# Get the number of classes\nnum_classes = len(validation_generator.class_indices)\n\n# Define the class labels\nclass_labels = list(validation_generator.class_indices.keys())\n\n# Make predictions on the validation data\nvalidation_predictions = model.predict(validation_generator)\npredicted_labels_validation = np.argmax(validation_predictions, axis=1)\n\n# Get the true labels for the validation data\ntrue_labels_validation = validation_generator.classes\n\n# Calculate confusion matrix for validation data\nconf_matrix_validation = confusion_matrix(true_labels_validation, predicted_labels_validation)\n\n# Plot the confusion matrix\nplt.figure(figsize=(15, 10))\nsns.heatmap(conf_matrix_validation, annot=True, fmt='d', xticklabels=class_labels, yticklabels=class_labels, cmap='Reds')\nplt.xlabel('Predicted Label')\nplt.ylabel('True Label')\nplt.title('Confusion Matrix')\nplt.xticks(rotation=90)\nplt.show()\n", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:43:35.068224Z", "iopub.execute_input": "2024-06-13T09:43:35.068483Z", "iopub.status.idle": "2024-06-13T09:43:43.664874Z", "shell.execute_reply.started": "2024-06-13T09:43:35.068454Z", "shell.execute_reply": "2024-06-13T09:43:43.663928Z"}, "trusted": true}, "execution_count": 13, "outputs": [{"name": "stdout", "text": "Found 278 images belonging to 15 classes.\n\u001b[1m9/9\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m7s\u001b[0m 512ms/step\n", "output_type": "stream"}, {"output_type": "display_data", "data": {"text/plain": "<Figure size 1500x1000 with 2 Axes>", "image/png": "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"}, "metadata": {}}]}, {"cell_type": "markdown", "source": "# Plot confusion matrix", "metadata": {}}, {"cell_type": "markdown", "source": "# Save the model", "metadata": {}}, {"cell_type": "code", "source": "model.save('birds_best_classes.h5')", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:57:18.591925Z", "iopub.execute_input": "2024-06-13T09:57:18.592758Z", "iopub.status.idle": "2024-06-13T09:57:19.101822Z", "shell.execute_reply.started": "2024-06-13T09:57:18.592726Z", "shell.execute_reply": "2024-06-13T09:57:19.101036Z"}, "trusted": true}, "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": "from IPython.display import FileLink\nFileLink(r'birds_best_classes.h5')", "metadata": {"execution": {"iopub.status.busy": "2024-06-13T09:57:22.345905Z", "iopub.execute_input": "2024-06-13T09:57:22.346263Z", "iopub.status.idle": "2024-06-13T09:57:22.352990Z", "shell.execute_reply.started": "2024-06-13T09:57:22.346236Z", "shell.execute_reply": "2024-06-13T09:57:22.352027Z"}, "trusted": true}, "execution_count": 16, "outputs": [{"execution_count": 16, "output_type": "execute_result", "data": {"text/plain": "/kaggle/working/birds_best_classes.h5", "text/html": "<a href='birds_best_classes.h5' target='_blank'>birds_best_classes.h5</a><br>"}, "metadata": {}}]}]}